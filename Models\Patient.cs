using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Midterm1212.Models
{
    public class Patient
    {
        public Patient()
        {
            Appointments = new HashSet<Appointment>();
            MedicalRecords = new HashSet<MedicalRecord>();
            Prescriptions = new HashSet<Prescription>();
            MedicationOrders = new HashSet<MedicationOrder>();
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        [Required]
        public int patient_id { get; set; }

        [Required]
        public string name { get; set; } = string.Empty;

        [Required]
        [DataType(DataType.Date)]
        public DateTime dob { get; set; }

        [Required]
        public string gender { get; set; } = string.Empty;

        [Required]
        public string phone { get; set; } = string.Empty;

        [Required]
        public string address { get; set; } = string.Empty;

        public virtual ICollection<Appointment> Appointments { get; set; }
        public virtual ICollection<MedicalRecord> MedicalRecords { get; set; }
        public virtual ICollection<Prescription> Prescriptions { get; set; }
        public virtual ICollection<MedicationOrder> MedicationOrders { get; set; }
    }
}
