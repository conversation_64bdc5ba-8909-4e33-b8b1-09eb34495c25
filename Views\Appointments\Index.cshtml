﻿@model IEnumerable<Midterm1212.Models.Appointment>

@{
    ViewData["Title"] = "Quản lý lịch hẹn";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h2>@ViewData["Title"]</h2>
                @if (User.IsInRole("Admin") || User.IsInRole("Doctor"))
                {
                    <a asp-action="Create" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Thêm lịch hẹn mới
                    </a>
                }
            </div>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <form id="updateStatusForm">
                @Html.AntiForgeryToken()
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Mã lịch hẹn</th>
                                    <th>Bệnh nhân</th>
                                    <th>Bác sĩ</th>
                                    <th>Ngày hẹn</th>
                                    <th>Giờ hẹn</th>
                                    <th>Trạng thái</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>
                                            <strong>#@Html.DisplayFor(modelItem => item.appointment_id)</strong>
                                        </td>
                                        <td>
                                            <strong>@Html.DisplayFor(modelItem => item.Patient.name)</strong>
                                            <br><small class="text-muted">@Html.DisplayFor(modelItem => item.Patient.phone)</small>
                                        </td>
                                        <td>
                                            <strong>BS. @Html.DisplayFor(modelItem => item.Doctor.name)</strong>
                                            <br><small class="text-muted">@Html.DisplayFor(modelItem => item.Doctor.specialization)</small>
                                        </td>
                                        <td>@item.appointment_date.ToString("dd/MM/yyyy")</td>
                                        <td><strong>@item.appointment_time.ToString(@"hh\:mm")</strong></td>
                                        <td>
                                            @switch (item.status)
                                            {
                                                case "Pending":
                                                    <span class="badge bg-warning">Chờ xác nhận</span>
                                                    break;
                                                case "Confirmed":
                                                    <span class="badge bg-info">Đã xác nhận</span>
                                                    break;
                                                case "Complete":
                                                    <span class="badge bg-success">Hoàn thành</span>
                                                    break;
                                                case "Cancelled":
                                                    <span class="badge bg-danger">Đã hủy</span>
                                                    break;
                                                default:
                                                    <span class="badge bg-secondary">@item.status</span>
                                                    break;
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@item.appointment_id"
                                                   class="btn btn-outline-info btn-sm" title="Xem chi tiết">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                @if (User.IsInRole("Admin") || User.IsInRole("Doctor"))
                                                {
                                                    <a asp-action="Edit" asp-route-id="@item.appointment_id"
                                                       class="btn btn-outline-primary btn-sm" title="Chỉnh sửa">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    @if (item.status == "Pending")
                                                    {
                                                            <button type="button" class="btn btn-outline-success btn-sm" title="Xác nhận"
                                                                onclick="updateStatus(@item.appointment_id, 'Confirmed')">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                    }
                                                    @if (item.status == "Confirmed")
                                                    {
                                                        <a asp-action="CreateMedicalRecord" asp-route-id="@item.appointment_id"
                                                           class="btn btn-outline-warning btn-sm" title="Hoàn tất khám bệnh">
                                                            <i class="fas fa-stethoscope"></i>
                                                        </a>
                                                    }
                                                    @if (item.status != "Complete" && item.status != "Cancelled")
                                                    {
                                                        <a asp-action="Delete" asp-route-id="@item.appointment_id"
                                                           class="btn btn-outline-danger btn-sm" title="Hủy lịch hẹn">
                                                            <i class="fas fa-times"></i>
                                                        </a>
                                                    }
                                                }
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function updateStatus(appointmentId, status) {
            if (confirm(`Bạn có chắc chắn muốn cập nhật trạng thái thành '${status}'?`)) {
                // Get antiforgery token
                var token = $('input[name="__RequestVerificationToken"]').val();

                $.ajax({
                    url: '@Url.Action("UpdateStatus", "Appointments")',
                    type: 'POST',
                    data: {
                        id: appointmentId,
                        status: status,
                        __RequestVerificationToken: token
                    },
                    success: function(result) {
                        if (result.success) {
                            // Reload the page to show updated status
                            location.reload();
                        } else {
                            alert(result.message || 'Có lỗi xảy ra khi cập nhật trạng thái.');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error:', error);
                        alert('Có lỗi xảy ra khi cập nhật trạng thái. Vui lòng thử lại.');
                    }
                });
            }
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    </script>
}