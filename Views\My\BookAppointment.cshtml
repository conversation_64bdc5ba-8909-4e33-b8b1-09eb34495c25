@model Midterm1212.Models.Appointment

@{
    ViewData["Title"] = "Đặt lịch hẹn";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-calendar-plus"></i> @ViewData["Title"]
                    </h4>
                </div>
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <form asp-action="BookAppointment" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="appointment_id" class="form-label">Mã lịch hẹn</label>
                                    <input asp-for="appointment_id" class="form-control" readonly />
                                    <span asp-validation-for="appointment_id" class="text-danger"></span>
                                    <div class="form-text">Mã lịch hẹn được tạo tự động</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="appointment_date" class="form-label">Ngày hẹn</label>
                                    <input asp-for="appointment_date" class="form-control" type="date" />
                                    <span asp-validation-for="appointment_date" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="doctor_id" class="form-label">Bác sĩ</label>
                                    @Html.DropDownList("doctor_id", ViewBag.doctor_id as SelectList, "-- Chọn bác sĩ --", new { @class = "form-select" })
                                    <span asp-validation-for="doctor_id" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="appointment_time" class="form-label">Giờ hẹn</label>
                                    <select asp-for="appointment_time" class="form-select">
                                        <option value="">-- Chọn giờ hẹn --</option>
                                        <option value="08:00:00">08:00</option>
                                        <option value="08:30:00">08:30</option>
                                        <option value="09:00:00">09:00</option>
                                        <option value="09:30:00">09:30</option>
                                        <option value="10:00:00">10:00</option>
                                        <option value="10:30:00">10:30</option>
                                        <option value="11:00:00">11:00</option>
                                        <option value="11:30:00">11:30</option>
                                        <option value="14:00:00">14:00</option>
                                        <option value="14:30:00">14:30</option>
                                        <option value="15:00:00">15:00</option>
                                        <option value="15:30:00">15:30</option>
                                        <option value="16:00:00">16:00</option>
                                        <option value="16:30:00">16:30</option>
                                        <option value="17:00:00">17:00</option>
                                    </select>
                                    <span asp-validation-for="appointment_time" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <input type="hidden" asp-for="status" value="Pending" />

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Lưu ý:</h6>
                            <ul class="mb-0">
                                <li>Vui lòng đặt lịch hẹn trước ít nhất 1 ngày</li>
                                <li>Lịch hẹn sẽ ở trạng thái "Chờ xác nhận" cho đến khi bác sĩ xác nhận</li>
                                <li>Bạn sẽ nhận được thông báo khi lịch hẹn được xác nhận</li>
                                <li>Có thể hủy lịch hẹn trước 2 giờ so với giờ hẹn</li>
                            </ul>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-action="Appointments" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại lịch hẹn
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-calendar-plus"></i> Đặt lịch hẹn
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Set minimum date to tomorrow
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        document.getElementById('appointment_date').min = tomorrow.toISOString().split('T')[0];
        
        // Set max date to 30 days from now
        const maxDate = new Date();
        maxDate.setDate(maxDate.getDate() + 30);
        document.getElementById('appointment_date').max = maxDate.toISOString().split('T')[0];
        
        // Auto-hide success messages
        setTimeout(function() {
            $('.alert-success').fadeOut('slow');
        }, 5000);
    </script>
}
