@model Midterm1212.Models.Prescription

@{
    ViewData["Title"] = "Tạo đơn thuốc mới";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1>T<PERSON><PERSON> đơn thuốc mới</h1>

            <form asp-action="Create" method="post">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="prescription_id" class="control-label">M<PERSON> đơn thuốc</label>
                            <input asp-for="prescription_id" class="form-control" />
                            <span asp-validation-for="prescription_id" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="patient_id" class="control-label">Bệnh nhân</label>
                            <select asp-for="patient_id" class="form-control" asp-items="ViewBag.patient_id">
                                <option value="">-- <PERSON><PERSON><PERSON> bệnh nhân --</option>
                            </select>
                            <span asp-validation-for="patient_id" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="doctor_id" class="control-label">Bác sĩ kê đơn</label>
                            <select asp-for="doctor_id" class="form-control" asp-items="ViewBag.doctor_id">
                                <option value="">-- Chọn bác sĩ --</option>
                            </select>
                            <span asp-validation-for="doctor_id" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="medical_record_id" class="control-label">Hồ sơ y tế (tùy chọn)</label>
                            <select asp-for="medical_record_id" class="form-control" asp-items="ViewBag.medical_record_id">
                                <option value="">-- Chọn hồ sơ y tế --</option>
                            </select>
                            <span asp-validation-for="medical_record_id" class="text-danger"></span>
                            <small class="form-text text-muted">Liên kết đơn thuốc với hồ sơ y tế cụ thể (không bắt buộc)</small>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="prescription_date" class="control-label">Ngày kê đơn</label>
                            <input asp-for="prescription_date" class="form-control" type="date" />
                            <span asp-validation-for="prescription_date" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="status" class="control-label">Trạng thái</label>
                            <select asp-for="status" class="form-control">
                                <option value="Chờ xử lý">Chờ xử lý</option>
                                <option value="Đã cấp thuốc">Đã cấp thuốc</option>
                                <option value="Hoàn thành">Hoàn thành</option>
                                <option value="Hủy">Hủy</option>
                            </select>
                            <span asp-validation-for="status" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="notes" class="control-label">Ghi chú</label>
                            <textarea asp-for="notes" class="form-control" rows="4" placeholder="Ghi chú về đơn thuốc, hướng dẫn đặc biệt..."></textarea>
                            <span asp-validation-for="notes" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle"></i> Lưu ý</h5>
                    <p>Sau khi tạo đơn thuốc, bạn sẽ được chuyển đến trang thêm thuốc để chọn các loại thuốc cần thiết cho đơn này.</p>
                </div>

                <div class="form-group">
                    <input type="submit" value="Tạo đơn thuốc" class="btn btn-primary" />
                    <a asp-action="Index" class="btn btn-secondary">Quay lại</a>
                </div>
            </form>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-md-12 text-left">
            © 2025 - Student's ID - Your Name
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Set default date to today
        $(document).ready(function() {
            if (!$('#prescription_date').val()) {
                var today = new Date().toISOString().split('T')[0];
                $('#prescription_date').val(today);
            }
        });

        // Load medical records when patient is selected
        $('#patient_id').change(function() {
            var patientId = $(this).val();
            if (patientId) {
                $.get('/MedicalRecords/GetByPatient/' + patientId, function(data) {
                    var medicalRecordSelect = $('#medical_record_id');
                    medicalRecordSelect.empty();
                    medicalRecordSelect.append('<option value="">-- Chọn hồ sơ y tế --</option>');
                    
                    $.each(data, function(index, record) {
                        medicalRecordSelect.append('<option value="' + record.record_id + '">' + 
                                                 record.diagnosis + ' (' + record.record_date + ')</option>');
                    });
                });
            } else {
                $('#medical_record_id').empty().append('<option value="">-- Chọn hồ sơ y tế --</option>');
            }
        });
    </script>
}
