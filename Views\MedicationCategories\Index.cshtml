@model IEnumerable<Midterm1212.Models.MedicationCategory>

@{
    ViewData["Title"] = "Quản lý danh mục thuốc";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1>Quản lý danh mục thuốc</h1>
            
            <p>
                <a asp-action="Create" class="btn btn-success">Thêm danh mục mới</a>
            </p>

            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead class="thead-dark">
                        <tr>
                            <th><PERSON><PERSON> danh mục</th>
                            <th>Tên danh mục</th>
                            <th><PERSON><PERSON> tả</th>
                            <th>Số lượng thuốc</th>
                            <th>Ng<PERSON>y tạo</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>@Html.DisplayFor(modelItem => item.category_id)</td>
                                <td><strong>@Html.DisplayFor(modelItem => item.category_name)</strong></td>
                                <td>@Html.DisplayFor(modelItem => item.description)</td>
                                <td>
                                    <span class="badge badge-info">@item.Medications.Count() thuốc</span>
                                </td>
                                <td>@item.created_date.ToString("dd/MM/yyyy")</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@item.category_id" class="btn btn-info btn-sm">Chi tiết</a>
                                        <a asp-action="Edit" asp-route-id="@item.category_id" class="btn btn-primary btn-sm">Sửa</a>
                                        @if (!item.Medications.Any())
                                        {
                                            <a asp-action="Delete" asp-route-id="@item.category_id" class="btn btn-danger btn-sm">Xóa</a>
                                        }
                                        else
                                        {
                                            <button class="btn btn-secondary btn-sm" disabled title="Không thể xóa danh mục có thuốc">Xóa</button>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            @if (!Model.Any())
            {
                <div class="alert alert-info text-center">
                    <h4>Chưa có danh mục thuốc nào</h4>
                    <p>Hãy <a asp-action="Create">tạo danh mục đầu tiên</a> để bắt đầu phân loại thuốc.</p>
                </div>
            }
            else
            {
                <div class="alert alert-info">
                    <strong>Thống kê:</strong>
                    <br />
                    <strong>Tổng số danh mục:</strong> @Model.Count()
                    <br />
                    <strong>Tổng số thuốc:</strong> @Model.Sum(c => c.Medications.Count())
                    <br />
                    <strong>Danh mục có nhiều thuốc nhất:</strong> @(Model.Any() ? Model.OrderByDescending(c => c.Medications.Count()).First().category_name + " (" + Model.OrderByDescending(c => c.Medications.Count()).First().Medications.Count() + " thuốc)" : "Không có")
                </div>
            }
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-md-12 text-left">
            © 2025 - Student's ID - Your Name
        </div>
    </div>
</div>

<style>
    .btn-group .btn {
        margin-right: 2px;
    }
</style>
