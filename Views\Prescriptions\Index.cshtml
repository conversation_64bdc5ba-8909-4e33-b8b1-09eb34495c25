@model IEnumerable<Midterm1212.Models.Prescription>

@{
    ViewData["Title"] = "Quản lý đơn thuốc";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1><PERSON>u<PERSON>n lý đơn thuốc</h1>
            
            <!-- Search and Filter Form -->
            <form method="get" class="mb-3">
                <div class="row">
                    <div class="col-md-3">
                        <input type="text" name="searchString" value="@ViewData["CurrentFilter"]" 
                               class="form-control" placeholder="Tìm kiếm theo tên bệnh nhân, bác sĩ..." />
                    </div>
                    <div class="col-md-2">
                        <select name="status" class="form-control">
                            <option value="">-- Tất cả trạng thái --</option>
                            @foreach (var status in ViewBag.StatusList)
                            {
                                <option value="@status.Value" selected="@(status.Value == ViewData["CurrentStatus"]?.ToString())">
                                    @status.Text
                                </option>
                            }
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select name="patientId" class="form-control">
                            <option value="">-- Tất cả bệnh nhân --</option>
                            @foreach (var patient in ViewBag.Patients)
                            {
                                <option value="@patient.Value" selected="@(patient.Value == ViewData["CurrentPatient"]?.ToString())">
                                    @patient.Text
                                </option>
                            }
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select name="doctorId" class="form-control">
                            <option value="">-- Tất cả bác sĩ --</option>
                            @foreach (var doctor in ViewBag.Doctors)
                            {
                                <option value="@doctor.Value" selected="@(doctor.Value == ViewData["CurrentDoctor"]?.ToString())">
                                    @doctor.Text
                                </option>
                            }
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary">Tìm kiếm</button>
                    </div>
                    <div class="col-md-1">
                        <a asp-action="Create" class="btn btn-success">Tạo đơn mới</a>
                    </div>
                </div>
            </form>

            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead class="thead-dark">
                        <tr>
                            <th>Mã đơn</th>
                            <th>Ngày kê đơn</th>
                            <th>Bệnh nhân</th>
                            <th>Bác sĩ</th>
                            <th>Số loại thuốc</th>
                            <th>Tổng tiền</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>@Html.DisplayFor(modelItem => item.prescription_id)</td>
                                <td>@item.prescription_date.ToString("dd/MM/yyyy")</td>
                                <td>@Html.DisplayFor(modelItem => item.Patient.name)</td>
                                <td>@Html.DisplayFor(modelItem => item.Doctor.name)</td>
                                <td>@item.PrescriptionItems.Count()</td>
                                <td>@item.total_amount.ToString("N0") VNĐ</td>
                                <td>
                                    @switch (item.status)
                                    {
                                        case "Chờ xử lý":
                                            <span class="badge badge-warning">@item.status</span>
                                            break;
                                        case "Đã cấp thuốc":
                                            <span class="badge badge-info">@item.status</span>
                                            break;
                                        case "Hoàn thành":
                                            <span class="badge badge-success">@item.status</span>
                                            break;
                                        case "Hủy":
                                            <span class="badge badge-danger">@item.status</span>
                                            break;
                                        default:
                                            <span class="badge badge-secondary">@item.status</span>
                                            break;
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@item.prescription_id" class="btn btn-info btn-sm">Chi tiết</a>
                                        @if (item.status == "Chờ xử lý")
                                        {
                                            <a asp-action="Edit" asp-route-id="@item.prescription_id" class="btn btn-primary btn-sm">Sửa</a>
                                            <a asp-action="AddItems" asp-route-id="@item.prescription_id" class="btn btn-success btn-sm">Thêm thuốc</a>
                                        }
                                        <a asp-action="Delete" asp-route-id="@item.prescription_id" class="btn btn-danger btn-sm">Xóa</a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            @if (!Model.Any())
            {
                <div class="alert alert-info text-center">
                    <h4>Không tìm thấy đơn thuốc nào</h4>
                    <p>Hãy thử thay đổi tiêu chí tìm kiếm hoặc <a asp-action="Create">tạo đơn thuốc mới</a>.</p>
                </div>
            }
            else
            {
                <div class="alert alert-info">
                    <strong>Thống kê:</strong>
                    <br />
                    <strong>Tổng số đơn thuốc:</strong> @Model.Count()
                    <br />
                    <strong>Chờ xử lý:</strong> @Model.Count(p => p.status == "Chờ xử lý")
                    <br />
                    <strong>Đã cấp thuốc:</strong> @Model.Count(p => p.status == "Đã cấp thuốc")
                    <br />
                    <strong>Hoàn thành:</strong> @Model.Count(p => p.status == "Hoàn thành")
                    <br />
                    <strong>Tổng giá trị:</strong> @Model.Sum(p => p.total_amount).ToString("N0") VNĐ
                </div>
            }
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-md-12 text-left">
            © 2025 - Student's ID - Your Name
        </div>
    </div>
</div>

<style>
    .btn-group .btn {
        margin-right: 2px;
    }
</style>
