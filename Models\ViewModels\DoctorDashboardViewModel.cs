using Midterm1212.Models;

namespace Midterm1212.Models.ViewModels
{
    public class DoctorDashboardViewModel
    {
        public Doctor Doctor { get; set; }
        public List<Appointment> TodayAppointments { get; set; } = new List<Appointment>();
        public List<Appointment> PendingAppointments { get; set; } = new List<Appointment>();
        public List<MedicalRecord> RecentMedicalRecords { get; set; } = new List<MedicalRecord>();
        public int TotalPatients { get; set; }
        public int TotalPrescriptions { get; set; }
    }
} 