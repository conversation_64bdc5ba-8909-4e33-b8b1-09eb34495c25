using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Midterm1212.Models
{
    public class MedicalRecord
    {
        public MedicalRecord()
        {
            Prescriptions = new HashSet<Prescription>();
            // Set default values for required fields
            diagnosis = string.Empty;
            prescription = string.Empty;
            treatment = string.Empty;
            notes = string.Empty;
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        [Display(Name = "<PERSON><PERSON> hồ sơ")]
        public int record_id { get; set; }

        [Required(ErrorMessage = "Vui lòng chọn bệnh nhân")]
        [Display(Name = "Bệnh nhân")]
        public int patient_id { get; set; }

        [Required(ErrorMessage = "Vui lòng chọn bác sĩ")]
        [Display(Name = "<PERSON><PERSON><PERSON> sĩ")]
        public int doctor_id { get; set; }

        [Required(ErrorMessage = "<PERSON>ui lòng chọn lịch hẹn")]
        [Display(Name = "Lịch hẹn")]
        public int appointment_id { get; set; }

        [Required(ErrorMessage = "<PERSON>ui lòng nhập chẩn đoán")]
        [Display(Name = "<PERSON>ẩn đoán")]
        [StringLength(500, ErrorMessage = "Chẩn đoán không được vượt quá 500 ký tự")]
        public string diagnosis { get; set; }

        [Display(Name = "Điều trị")]
        [StringLength(1000, ErrorMessage = "Điều trị không được vượt quá 1000 ký tự")]
        public string treatment { get; set; }

        [Display(Name = "Ghi chú")]
        [StringLength(1000, ErrorMessage = "Ghi chú không được vượt quá 1000 ký tự")]
        public string notes { get; set; }

        [Required]
        [Display(Name = "Ngày khám")]
        [DataType(DataType.DateTime)]
        public DateTime record_date { get; set; }

        // Legacy field for compatibility
        [Display(Name = "Đơn thuốc (cũ)")]
        public string prescription { get; set; }

        [ForeignKey("patient_id")]
        public virtual Patient Patient { get; set; }
        
        [ForeignKey("doctor_id")]
        public virtual Doctor Doctor { get; set; }
        
        [ForeignKey("appointment_id")]
        public virtual Appointment Appointment { get; set; }

        public virtual ICollection<Prescription> Prescriptions { get; set; }
    }
}
