@model Midterm1212.Models.Prescription

@{
    ViewData["Title"] = "Kết quả khám bệnh và đơn thuốc";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="text-center mb-4">
                <h2 class="text-success">
                    <i class="fas fa-check-circle"></i> <PERSON>àn tất khám bệnh
                </h2>
                <p class="text-muted"><PERSON><PERSON> sơ bệnh án và đơn thuốc đã được tạo thành công</p>
            </div>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <div class="row">
                <!-- Medical Record Summary -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-file-medical"></i> Hồ sơ bệnh án #@Model.MedicalRecord.record_id
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <dl class="row">
                                        <dt class="col-sm-5">Bệnh nhân:</dt>
                                        <dd class="col-sm-7"><strong>@Model.MedicalRecord.Patient.name</strong></dd>
                                        
                                        <dt class="col-sm-5">Tuổi:</dt>
                                        <dd class="col-sm-7">@((DateTime.Now - Model.MedicalRecord.Patient.dob).Days / 365) tuổi</dd>
                                        
                                        <dt class="col-sm-5">Giới tính:</dt>
                                        <dd class="col-sm-7">@Model.MedicalRecord.Patient.gender</dd>
                                        
                                        <dt class="col-sm-5">Điện thoại:</dt>
                                        <dd class="col-sm-7">@Model.MedicalRecord.Patient.phone</dd>
                                    </dl>
                                </div>
                                <div class="col-md-6">
                                    <dl class="row">
                                        <dt class="col-sm-5">Bác sĩ:</dt>
                                        <dd class="col-sm-7"><strong>BS. @Model.MedicalRecord.Doctor.name</strong></dd>
                                        
                                        <dt class="col-sm-5">Chuyên khoa:</dt>
                                        <dd class="col-sm-7">@Model.MedicalRecord.Doctor.specialization</dd>
                                        
                                        <dt class="col-sm-5">Ngày khám:</dt>
                                        <dd class="col-sm-7">@Model.MedicalRecord.record_date.ToString("dd/MM/yyyy")</dd>
                                        
                                        <dt class="col-sm-5">Giờ khám:</dt>
                                        <dd class="col-sm-7">@Model.MedicalRecord.Appointment.appointment_time.ToString(@"hh\:mm")</dd>
                                    </dl>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="mb-3">
                                <h6>Chẩn đoán:</h6>
                                <p class="bg-light p-2 rounded"><strong>@Model.MedicalRecord.diagnosis</strong></p>
                            </div>
                            
                            @if (!string.IsNullOrEmpty(Model.MedicalRecord.treatment))
                            {
                                <div class="mb-3">
                                    <h6>Điều trị:</h6>
                                    <p class="bg-light p-2 rounded">@Model.MedicalRecord.treatment</p>
                                </div>
                            }
                            
                            @if (!string.IsNullOrEmpty(Model.MedicalRecord.notes))
                            {
                                <div class="mb-3">
                                    <h6>Ghi chú:</h6>
                                    <p class="bg-light p-2 rounded">@Model.MedicalRecord.notes</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <!-- Prescription Details -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-prescription"></i> Đơn thuốc #@Model.Id
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <dl class="row">
                                    <dt class="col-sm-4">Ngày kê đơn:</dt>
                                    <dd class="col-sm-8">@Model.PrescriptionDate.ToString("dd/MM/yyyy HH:mm")</dd>
                                    
                                    <dt class="col-sm-4">Trạng thái:</dt>
                                    <dd class="col-sm-8">
                                        <span class="badge bg-warning">@Model.Status</span>
                                    </dd>
                                    
                                    <dt class="col-sm-4">Tổng tiền:</dt>
                                    <dd class="col-sm-8"><strong class="text-primary">@Model.TotalCost.ToString("C")</strong></dd>
                                </dl>
                            </div>

                            @if (!string.IsNullOrEmpty(Model.Instructions))
                            {
                                <div class="mb-3">
                                    <h6>Hướng dẫn sử dụng:</h6>
                                    <p class="bg-light p-2 rounded">@Model.Instructions</p>
                                </div>
                            }

                            <h6>Chi tiết thuốc:</h6>
                            @if (Model.PrescriptionDetails != null && Model.PrescriptionDetails.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Tên thuốc</th>
                                                <th>SL</th>
                                                <th>Liều dùng</th>
                                                <th>Thành tiền</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var detail in Model.PrescriptionDetails)
                                            {
                                                <tr>
                                                    <td>
                                                        <strong>@detail.Medicine.Name</strong>
                                                        @if (!string.IsNullOrEmpty(detail.Instructions))
                                                        {
                                                            <br><small class="text-muted">@detail.Instructions</small>
                                                        }
                                                    </td>
                                                    <td>@detail.Quantity</td>
                                                    <td>
                                                        @detail.Dosage
                                                        @if (!string.IsNullOrEmpty(detail.Frequency))
                                                        {
                                                            <br><small>@detail.Frequency</small>
                                                        }
                                                        @if (!string.IsNullOrEmpty(detail.Duration))
                                                        {
                                                            <br><small>(@detail.Duration)</small>
                                                        }
                                                    </td>
                                                    <td><strong>@detail.TotalPrice.ToString("C")</strong></td>
                                                </tr>
                                            }
                                        </tbody>
                                        <tfoot>
                                            <tr class="table-primary">
                                                <th colspan="3">Tổng cộng:</th>
                                                <th>@Model.TotalCost.ToString("C")</th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <p class="text-muted">Không có thuốc trong đơn</p>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Thao tác tiếp theo</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="d-grid">
                                        <a href="#" class="btn btn-info" onclick="printPrescription()">
                                            <i class="fas fa-print"></i><br>
                                            In đơn thuốc
                                        </a>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="d-grid">
                                        <a asp-controller="MedicationOrders" asp-action="Create" asp-route-prescriptionId="@Model.Id" class="btn btn-success">
                                            <i class="fas fa-truck"></i><br>
                                            Đặt thuốc COD
                                        </a>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="d-grid">
                                        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning">
                                            <i class="fas fa-edit"></i><br>
                                            Sửa đơn thuốc
                                        </a>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="d-grid">
                                        <a asp-controller="Appointments" asp-action="Index" class="btn btn-secondary">
                                            <i class="fas fa-list"></i><br>
                                            Danh sách lịch hẹn
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Patient Instructions -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> Thông tin cho bệnh nhân:</h6>
                        <ul class="mb-0">
                            <li><strong>Đơn thuốc đã được tạo</strong> và sẵn sàng để đặt hàng COD</li>
                            <li><strong>Bệnh nhân có thể đặt thuốc</strong> thông qua hệ thống và nhận hàng tại nhà</li>
                            <li><strong>Thanh toán khi nhận hàng</strong> (COD) - không cần trả trước</li>
                            <li><strong>Liên hệ hotline</strong> nếu có thắc mắc về đơn thuốc</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function printPrescription() {
            window.print();
        }
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
        
        // Show success animation
        $(document).ready(function() {
            $('.fa-check-circle').addClass('animate__animated animate__bounceIn');
        });
    </script>
    
    <style>
        @@media print {
            .btn, .alert, .card-header {
                display: none !important;
            }
            .card {
                border: none !important;
                box-shadow: none !important;
            }
        }
    </style>
}
