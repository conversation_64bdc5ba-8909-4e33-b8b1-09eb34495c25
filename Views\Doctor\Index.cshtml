@using Midterm1212.Models
@model PaginatedList<Doctor>

@{
    ViewData["Title"] = "Danh sách bác sĩ";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>@ViewData["Title"]</h2>
                @if (User.IsInRole("Admin"))
                {
                    <a asp-action="Create" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Thêm bác sĩ mới
                    </a>
                }
            </div>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }
        </div>
    </div>

<div class="row">
    @foreach (var item in Model)
    {
        <div class="col-md-4 mb-4">
            <div class="card" style="border: 1px solid #ddd; background-color: #f8f9fa;">
                <div class="card-body">
                    <h5 class="card-title text-primary font-weight-bold">BS. @Html.DisplayFor(modelItem => item.name)</h5>

                    <p class="card-text">
                        <strong>Chuyên khoa:</strong> @Html.DisplayFor(modelItem => item.specialization)<br>
                        <strong>Điện thoại:</strong> @Html.DisplayFor(modelItem => item.phone)<br>
                        <strong>Email:</strong> @Html.DisplayFor(modelItem => item.email)
                    </p>

                    <div class="text-center mb-3">
                        @if (!string.IsNullOrEmpty(item.image))
                        {
                            <img src="@item.image" alt="Doctor Image" class="img-fluid" style="max-height: 150px; width: auto; border-radius: 5px;">
                        }
                        else
                        {
                            <div class="bg-light d-flex align-items-center justify-content-center" style="height: 150px; border-radius: 5px;">
                                <i class="fas fa-user-md fa-3x text-muted"></i>
                            </div>
                        }
                    </div>

                    <div class="text-center">
                        <div class="btn-group" role="group">
                            <a asp-action="Details" asp-route-id="@item.doctor_id"
                               class="btn btn-outline-info btn-sm" title="Xem chi tiết">
                                <i class="fas fa-eye"></i> Chi tiết
                            </a>
                            @if (User.IsInRole("Patient"))
                            {
                                <a asp-controller="My" asp-action="BookAppointment" asp-route-doctorId="@item.doctor_id"
                                   class="btn btn-primary btn-sm" title="Đặt lịch hẹn">
                                    <i class="fas fa-calendar-plus"></i> Đặt hẹn
                                </a>
                            }
                            @if (User.IsInRole("Admin"))
                            {
                                <a asp-action="Edit" asp-route-id="@item.doctor_id"
                                   class="btn btn-outline-primary btn-sm" title="Chỉnh sửa">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a asp-action="Delete" asp-route-id="@item.doctor_id"
                                   class="btn btn-outline-danger btn-sm" title="Xóa">
                                    <i class="fas fa-trash"></i>
                                </a>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <span class="text-muted">Page @Model.PageIndex / @Model.TotalPages</span>
            <nav aria-label="Page navigation">
                <ul class="pagination pagination-sm mb-0">
                    @if (Model.HasPreviousPage)
                    {
                        <li class="page-item">
                            <a class="page-link" href="@Url.Action("Index", new { pageNumber = Model.PageIndex - 1 })">«</a>
                        </li>
                    }

                    @for (int i = Math.Max(1, Model.PageIndex - 2); i <= Math.Min(Model.TotalPages, Model.PageIndex + 2); i++)
                    {
                        <li class="page-item @(i == Model.PageIndex ? "active" : "")">
                            <a class="page-link" href="@Url.Action("Index", new { pageNumber = i })">@i</a>
                        </li>
                    }

                    @if (Model.HasNextPage)
                    {
                        <li class="page-item">
                            <a class="page-link" href="@Url.Action("Index", new { pageNumber = Model.PageIndex + 1 })">»</a>
                        </li>
                    }
                </ul>
            </nav>
        </div>
    </div>
</div>
