using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Midterm1212.Models
{
    public class Medicine
    {
        public Medicine()
        {
            PrescriptionDetails = new HashSet<PrescriptionDetail>();
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        [Required]
        public int Id { get; set; }

        [Required]
        [Display(Name = "Tên thuốc")]
        public string Name { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Mô tả")]
        public string Description { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Số lượng tồn kho")]
        public int QuantityInStock { get; set; }

        [Required]
        [Display(Name = "Ngày hết hạn")]
        [DataType(DataType.Date)]
        public DateTime ExpirationDate { get; set; }

        [Required]
        [Display(Name = "Giá")]
        [Column(TypeName = "decimal(10,2)")]
        public decimal Price { get; set; }

        [Display(Name = "Nhà sản xuất")]
        public string? Manufacturer { get; set; }

        [Display(Name = "Liều lượng")]
        public string? Dosage { get; set; }

        [Display(Name = "Dạng thuốc")]
        public string? Form { get; set; }

        [Display(Name = "Ngày tạo")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "Cảnh báo tồn kho thấp")]
        public int LowStockThreshold { get; set; } = 10;

        public virtual ICollection<PrescriptionDetail> PrescriptionDetails { get; set; }
    }
}
