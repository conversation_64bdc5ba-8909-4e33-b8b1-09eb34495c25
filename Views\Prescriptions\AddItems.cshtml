@model Midterm1212.Models.Prescription

@{
    ViewData["Title"] = "Thêm thuốc vào đơn";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1>Thêm thuốc vào đơn</h1>

            <!-- Prescription Info -->
            <div class="card mb-3">
                <div class="card-header">
                    <h4>Thông tin đơn thuốc #@Model.prescription_id</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Bệnh nhân:</strong> @Model.Patient.name<br />
                            <strong><PERSON><PERSON><PERSON> sĩ:</strong> @Model.Doctor.name<br />
                            <strong>Ngày kê đơn:</strong> @Model.prescription_date.ToString("dd/MM/yyyy")
                        </div>
                        <div class="col-md-6">
                            <strong>Trạng thái:</strong> @Model.status<br />
                            <strong>Tổng tiền hiện tại:</strong> @Model.total_amount.ToString("N0") VNĐ<br />
                            <strong>Số loại thuốc:</strong> @Model.PrescriptionItems.Count()
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add Medication Form -->
            <div class="card mb-3">
                <div class="card-header">
                    <h4>Thêm thuốc mới</h4>
                </div>
                <div class="card-body">
                    <form id="addMedicationForm">
                        <input type="hidden" id="prescriptionId" value="@Model.prescription_id" />
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="medicationId">Chọn thuốc</label>
                                    <select id="medicationId" class="form-control" required>
                                        <option value="">-- Chọn thuốc --</option>
                                        @foreach (var medication in ViewBag.medications)
                                        {
                                            <option value="@medication.Value">@medication.Text</option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="quantity">Số lượng</label>
                                    <input type="number" id="quantity" class="form-control" min="1" required />
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="dosageInstructions">Liều dùng</label>
                                    <input type="text" id="dosageInstructions" class="form-control" placeholder="VD: 1 viên" required />
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="frequency">Tần suất</label>
                                    <input type="text" id="frequency" class="form-control" placeholder="VD: 3 lần/ngày" required />
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="duration">Thời gian</label>
                                    <input type="text" id="duration" class="form-control" placeholder="VD: 7 ngày" required />
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-success form-control">Thêm</button>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="notes">Ghi chú</label>
                                    <input type="text" id="notes" class="form-control" placeholder="Ghi chú đặc biệt cho thuốc này..." />
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Current Prescription Items -->
            <div class="card">
                <div class="card-header">
                    <h4>Danh sách thuốc trong đơn</h4>
                </div>
                <div class="card-body">
                    @if (Model.PrescriptionItems.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Tên thuốc</th>
                                        <th>Số lượng</th>
                                        <th>Liều dùng</th>
                                        <th>Tần suất</th>
                                        <th>Thời gian</th>
                                        <th>Đơn giá</th>
                                        <th>Thành tiền</th>
                                        <th>Ghi chú</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in Model.PrescriptionItems)
                                    {
                                        <tr>
                                            <td>@item.Medication.medication_name</td>
                                            <td>@item.quantity</td>
                                            <td>@item.dosage_instructions</td>
                                            <td>@item.frequency</td>
                                            <td>@item.duration</td>
                                            <td>@item.unit_price.ToString("N0") VNĐ</td>
                                            <td>@item.total_price.ToString("N0") VNĐ</td>
                                            <td>@item.notes</td>
                                        </tr>
                                    }
                                </tbody>
                                <tfoot>
                                    <tr class="table-info">
                                        <th colspan="6">Tổng cộng:</th>
                                        <th>@Model.total_amount.ToString("N0") VNĐ</th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info text-center">
                            <h5>Chưa có thuốc nào trong đơn</h5>
                            <p>Hãy sử dụng form bên trên để thêm thuốc vào đơn.</p>
                        </div>
                    }
                </div>
            </div>

            <div class="mt-3">
                <a asp-action="Details" asp-route-id="@Model.prescription_id" class="btn btn-info">Xem chi tiết đơn</a>
                <a asp-action="Index" class="btn btn-secondary">Quay lại danh sách</a>
                @if (Model.PrescriptionItems.Any())
                {
                    <a asp-action="Edit" asp-route-id="@Model.prescription_id" class="btn btn-primary">Chỉnh sửa đơn</a>
                }
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-md-12 text-left">
            © 2025 - Student's ID - Your Name
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $('#addMedicationForm').submit(function(e) {
            e.preventDefault();
            
            var formData = {
                prescriptionId: $('#prescriptionId').val(),
                medicationId: $('#medicationId').val(),
                quantity: $('#quantity').val(),
                dosageInstructions: $('#dosageInstructions').val(),
                frequency: $('#frequency').val(),
                duration: $('#duration').val(),
                notes: $('#notes').val()
            };

            // Validate form
            if (!formData.medicationId || !formData.quantity || !formData.dosageInstructions || 
                !formData.frequency || !formData.duration) {
                alert('Vui lòng điền đầy đủ thông tin bắt buộc');
                return;
            }

            $.post('@Url.Action("AddItem", "Prescriptions")', formData)
                .done(function(response) {
                    if (response.success) {
                        alert(response.message);
                        location.reload(); // Reload to show updated list
                    } else {
                        alert('Lỗi: ' + response.message);
                    }
                })
                .fail(function() {
                    alert('Đã xảy ra lỗi khi thêm thuốc');
                });
        });

        // Clear form after successful addition
        function clearForm() {
            $('#medicationId').val('');
            $('#quantity').val('');
            $('#dosageInstructions').val('');
            $('#frequency').val('');
            $('#duration').val('');
            $('#notes').val('');
        }
    </script>
}
