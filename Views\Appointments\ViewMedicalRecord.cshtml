@model Midterm1212.Models.MedicalRecord

@{
    ViewData["Title"] = "Xem hồ sơ bệnh án";
}

<div class="container">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-file-medical"></i> @ViewData["Title"] #@Model.record_id
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Thông tin bệnh nhân:</h6>
                            <dl class="row">
                                <dt class="col-sm-5">Mã BN:</dt>
                                <dd class="col-sm-7">#@Model.Patient.patient_id</dd>
                                
                                <dt class="col-sm-5">Họ và tên:</dt>
                                <dd class="col-sm-7"><strong>@Model.Patient.name</strong></dd>
                                
                                <dt class="col-sm-5">Tu<PERSON><PERSON>:</dt>
                                <dd class="col-sm-7">@((DateTime.Now - Model.Patient.dob).Days / 365) tuổi</dd>
                                
                                <dt class="col-sm-5">Giới tính:</dt>
                                <dd class="col-sm-7">@Model.Patient.gender</dd>
                                
                                <dt class="col-sm-5">Điện thoại:</dt>
                                <dd class="col-sm-7">@Model.Patient.phone</dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <h6>Thông tin khám bệnh:</h6>
                            <dl class="row">
                                <dt class="col-sm-5">Bác sĩ:</dt>
                                <dd class="col-sm-7"><strong>BS. @Model.Doctor.name</strong></dd>
                                
                                <dt class="col-sm-5">Chuyên khoa:</dt>
                                <dd class="col-sm-7">@Model.Doctor.specialization</dd>
                                
                                <dt class="col-sm-5">Ngày khám:</dt>
                                <dd class="col-sm-7">@Model.record_date.ToString("dd/MM/yyyy")</dd>
                                
                                <dt class="col-sm-5">Giờ khám:</dt>
                                <dd class="col-sm-7">@Model.Appointment.appointment_time.ToString(@"hh\:mm")</dd>
                            </dl>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="mb-3">
                        <h6>Chẩn đoán:</h6>
                        <div class="bg-light p-3 rounded">
                            <strong>@Model.diagnosis</strong>
                        </div>
                    </div>
                    
                    @if (!string.IsNullOrEmpty(Model.treatment))
                    {
                        <div class="mb-3">
                            <h6>Điều trị:</h6>
                            <div class="bg-light p-3 rounded">
                                @Model.treatment
                            </div>
                        </div>
                    }
                    
                    @if (!string.IsNullOrEmpty(Model.notes))
                    {
                        <div class="mb-3">
                            <h6>Ghi chú:</h6>
                            <div class="bg-light p-3 rounded">
                                @Model.notes
                            </div>
                        </div>
                    }
                    
                    @if (Model.Prescriptions != null && Model.Prescriptions.Any())
                    {
                        <hr>
                        <h6>Đơn thuốc liên quan:</h6>
                        @foreach (var prescription in Model.Prescriptions)
                        {
                            <div class="card mb-3">
                                <div class="card-header">
                                    <div class="d-flex justify-content-between">
                                        <h6 class="mb-0">Đơn thuốc #@prescription.Id</h6>
                                        <span class="badge @(prescription.Status == "Chờ xử lý" ? "bg-warning" : prescription.Status == "Đã xử lý" ? "bg-success" : "bg-secondary")">
                                            @prescription.Status
                                        </span>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            @if (prescription.PrescriptionDetails != null && prescription.PrescriptionDetails.Any())
                                            {
                                                <div class="table-responsive">
                                                    <table class="table table-sm">
                                                        <thead>
                                                            <tr>
                                                                <th>Tên thuốc</th>
                                                                <th>SL</th>
                                                                <th>Liều dùng</th>
                                                                <th>Thành tiền</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            @foreach (var detail in prescription.PrescriptionDetails)
                                                            {
                                                                <tr>
                                                                    <td>
                                                                        <strong>@detail.Medicine.Name</strong>
                                                                        @if (!string.IsNullOrEmpty(detail.Instructions))
                                                                        {
                                                                            <br><small class="text-muted">@detail.Instructions</small>
                                                                        }
                                                                    </td>
                                                                    <td>@detail.Quantity</td>
                                                                    <td>
                                                                        @detail.Dosage
                                                                        @if (!string.IsNullOrEmpty(detail.Frequency))
                                                                        {
                                                                            <br><small>@detail.Frequency</small>
                                                                        }
                                                                    </td>
                                                                    <td><strong>@detail.TotalPrice.ToString("C")</strong></td>
                                                                </tr>
                                                            }
                                                        </tbody>
                                                        <tfoot>
                                                            <tr class="table-primary">
                                                                <th colspan="3">Tổng cộng:</th>
                                                                <th>@prescription.TotalCost.ToString("C")</th>
                                                            </tr>
                                                        </tfoot>
                                                    </table>
                                                </div>
                                            }
                                        </div>
                                        <div class="col-md-4">
                                            <dl class="row">
                                                <dt class="col-sm-6">Ngày kê:</dt>
                                                <dd class="col-sm-6">@prescription.PrescriptionDate.ToString("dd/MM/yyyy")</dd>
                                                
                                                <dt class="col-sm-6">Tổng tiền:</dt>
                                                <dd class="col-sm-6"><strong>@prescription.TotalCost.ToString("C")</strong></dd>
                                            </dl>
                                            
                                            @if (!string.IsNullOrEmpty(prescription.Instructions))
                                            {
                                                <div class="alert alert-info">
                                                    <h6>Hướng dẫn:</h6>
                                                    <p class="mb-0">@prescription.Instructions</p>
                                                </div>
                                            }
                                            
                                            <div class="d-grid gap-2">
                                                <a asp-controller="Prescriptions" asp-action="Details" asp-route-id="@prescription.Id" class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-eye"></i> Xem chi tiết
                                                </a>
                                                @if (prescription.Status == "Chờ xử lý")
                                                {
                                                    <a asp-controller="MedicationOrders" asp-action="CreateFromPrescription" asp-route-prescriptionId="@prescription.Id" class="btn btn-success btn-sm">
                                                        <i class="fas fa-truck"></i> Đặt thuốc COD
                                                    </a>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    }
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <a asp-controller="Appointments" asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại danh sách
                        </a>
                        <div>
                            @if (User.IsInRole("Admin") || User.IsInRole("Doctor"))
                            {
                                @if (!Model.Prescriptions.Any())
                                {
                                    <a asp-controller="Prescriptions" asp-action="CreatePrescription" asp-route-medicalRecordId="@Model.record_id" class="btn btn-primary">
                                        <i class="fas fa-prescription"></i> Kê đơn thuốc
                                    </a>
                                }
                            }
                            <a href="#" class="btn btn-info" onclick="printRecord()">
                                <i class="fas fa-print"></i> In hồ sơ
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Thông tin lịch hẹn</h6>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-5">Mã lịch hẹn:</dt>
                        <dd class="col-sm-7">#@Model.Appointment.appointment_id</dd>
                        
                        <dt class="col-sm-5">Ngày hẹn:</dt>
                        <dd class="col-sm-7">@Model.Appointment.appointment_date.ToString("dd/MM/yyyy")</dd>
                        
                        <dt class="col-sm-5">Giờ hẹn:</dt>
                        <dd class="col-sm-7">@Model.Appointment.appointment_time.ToString(@"hh\:mm")</dd>
                        
                        <dt class="col-sm-5">Trạng thái:</dt>
                        <dd class="col-sm-7">
                            <span class="badge bg-success">@Model.Appointment.status</span>
                        </dd>
                    </dl>
                    
                    <div class="d-grid">
                        <a asp-controller="Appointments" asp-action="Details" asp-route-id="@Model.Appointment.appointment_id" class="btn btn-outline-info">
                            <i class="fas fa-calendar-alt"></i> Xem chi tiết lịch hẹn
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">Thao tác nhanh</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a asp-controller="Patients" asp-action="Details" asp-route-id="@Model.Patient.patient_id" class="btn btn-outline-primary">
                            <i class="fas fa-user"></i> Hồ sơ bệnh nhân
                        </a>
                        <a asp-controller="Doctor" asp-action="Details" asp-route-id="@Model.Doctor.doctor_id" class="btn btn-outline-success">
                            <i class="fas fa-user-md"></i> Hồ sơ bác sĩ
                        </a>
                        @if (Model.Prescriptions.Any())
                        {
                            <a asp-controller="My" asp-action="Prescriptions" class="btn btn-outline-warning">
                                <i class="fas fa-prescription"></i> Đơn thuốc của tôi
                            </a>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function printRecord() {
            window.print();
        }
    </script>
    
    <style>
        @@media print {
            .btn, .card-footer {
                display: none !important;
            }
            .card {
                border: none !important;
                box-shadow: none !important;
            }
        }
    </style>
}
