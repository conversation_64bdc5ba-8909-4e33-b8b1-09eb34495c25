@model IEnumerable<Midterm1212.Models.Medication>

@{
    ViewData["Title"] = "Thuốc tồn kho thấp";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1>T<PERSON><PERSON><PERSON> tồn kho thấp</h1>
            
            <div class="alert alert-warning">
                <h4><i class="fas fa-exclamation-triangle"></i> Cảnh báo tồn kho</h4>
                <p><PERSON><PERSON> sách các thuốc có số lượng tồn kho thấp hoặc hết hàng. Vui lòng nhập thêm hàng sớm nhất có thể.</p>
            </div>

            <p>
                <a asp-action="Index" class="btn btn-secondary">Quay lại danh sách thuốc</a>
                <a asp-action="Create" class="btn btn-success">Thêm thuốc mới</a>
            </p>

            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead class="thead-dark">
                        <tr>
                            <th>M<PERSON> thuốc</th>
                            <th>Tên thuốc</th>
                            <th>Danh mục</th>
                            <th>Tồn kho hiện tại</th>
                            <th>Tồn kho tối thiểu</th>
                            <th>Trạng thái</th>
                            <th>Giá bán</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model.OrderBy(m => m.stock_quantity))
                        {
                            <tr class="@(item.stock_quantity == 0 ? "table-danger" : "table-warning")">
                                <td>@Html.DisplayFor(modelItem => item.medication_id)</td>
                                <td>@Html.DisplayFor(modelItem => item.medication_name)</td>
                                <td>@Html.DisplayFor(modelItem => item.Category.category_name)</td>
                                <td>
                                    <span class="font-weight-bold @(item.stock_quantity == 0 ? "text-danger" : "text-warning")">
                                        @Html.DisplayFor(modelItem => item.stock_quantity)
                                    </span>
                                </td>
                                <td>@Html.DisplayFor(modelItem => item.minimum_stock)</td>
                                <td>
                                    @if (item.stock_quantity == 0)
                                    {
                                        <span class="badge badge-danger">Hết hàng</span>
                                    }
                                    else
                                    {
                                        <span class="badge badge-warning">Tồn kho thấp</span>
                                    }
                                </td>
                                <td>@item.unit_price.ToString("N0") VNĐ</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@item.medication_id" class="btn btn-info btn-sm">Chi tiết</a>
                                        <a asp-action="Edit" asp-route-id="@item.medication_id" class="btn btn-primary btn-sm">Cập nhật kho</a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            @if (!Model.Any())
            {
                <div class="alert alert-success text-center">
                    <h4><i class="fas fa-check-circle"></i> Tuyệt vời!</h4>
                    <p>Hiện tại không có thuốc nào có tồn kho thấp.</p>
                    <a asp-action="Index" class="btn btn-primary">Quay lại danh sách thuốc</a>
                </div>
            }
            else
            {
                <div class="alert alert-info">
                    <strong>Tổng số thuốc cần nhập thêm:</strong> @Model.Count() thuốc
                    <br />
                    <strong>Số thuốc hết hàng:</strong> @Model.Count(m => m.stock_quantity == 0) thuốc
                    <br />
                    <strong>Số thuốc tồn kho thấp:</strong> @Model.Count(m => m.stock_quantity > 0 && m.stock_quantity <= m.minimum_stock) thuốc
                </div>
            }
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-md-12 text-left">
            © 2025 - Student's ID - Your Name
        </div>
    </div>
</div>

<style>
    .table-warning {
        background-color: #fff3cd !important;
    }
    .table-danger {
        background-color: #f8d7da !important;
    }
</style>
