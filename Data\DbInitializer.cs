using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Midterm1212.Models;

namespace Midterm1212.Data
{
    public static class DbInitializer
    {
        public static async Task Initialize(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<MedicalDbContext>();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<ApplicationUser>>();
            var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<IdentityRole>>();

            // Ensure database is created
            await context.Database.EnsureCreatedAsync();

            // Create roles
            await CreateRoles(roleManager);

            // Create admin user
            await CreateAdminUser(userManager);

            // Create doctor accounts for existing doctors
            await CreateDoctorAccounts(context, userManager);
        }

        private static async Task CreateRoles(RoleManager<IdentityRole> roleManager)
        {
            string[] roleNames = { "Admin", "Doctor", "Patient" };

            foreach (var roleName in roleNames)
            {
                var roleExist = await roleManager.RoleExistsAsync(roleName);
                if (!roleExist)
                {
                    await roleManager.CreateAsync(new IdentityRole(roleName));
                }
            }
        }

        private static async Task CreateAdminUser(UserManager<ApplicationUser> userManager)
        {
            var adminUser = await userManager.FindByNameAsync("********");
            if (adminUser == null)
            {
                adminUser = new ApplicationUser
                {
                    UserName = "********",
                    Email = "<EMAIL>",
                    UserType = UserType.Admin,
                    FullName = "Quản trị viên hệ thống",
                    EmailConfirmed = true,
                    IsActive = true
                };

                var result = await userManager.CreateAsync(adminUser, "123456");
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(adminUser, "Admin");
                }
            }
        }

        private static async Task CreateDoctorAccounts(MedicalDbContext context, UserManager<ApplicationUser> userManager)
        {
            var doctors = await context.Doctors
                .Where(d => !context.Users.Any(u => u.DoctorId == d.doctor_id))
                .ToListAsync();

            foreach (var doctor in doctors)
            {
                var doctorUser = new ApplicationUser
                {
                    UserName = doctor.phone,
                    Email = doctor.email,
                    UserType = UserType.Doctor,
                    DoctorId = doctor.doctor_id,
                    FullName = doctor.name,
                    PhoneNumber = doctor.phone,
                    EmailConfirmed = true,
                    IsActive = true
                };

                var result = await userManager.CreateAsync(doctorUser, "123123");
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(doctorUser, "Doctor");
                }
            }
        }
    }
}
