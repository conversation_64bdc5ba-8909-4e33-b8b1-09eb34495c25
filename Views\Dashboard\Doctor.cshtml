@model Midterm1212.Models.ViewModels.DoctorDashboardViewModel
@{
    ViewData["Title"] = "Trang chủ bác sĩ";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-success">
                    <i class="fas fa-user-md"></i> <PERSON><PERSON><PERSON> m<PERSON>, BS. @Model.Doctor.name
                </h2>
                <div>
                    <span class="badge bg-info fs-6">@Model.Doctor.specialization</span>
                    <a asp-controller="Account" asp-action="ChangePassword" class="btn btn-outline-warning ms-2">
                        <i class="fas fa-key"></i> Đ<PERSON>i mật khẩu
                    </a>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>@Model.TodayAppointments.Count</h4>
                                    <p class="mb-0">Lịch hẹn hôm nay</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calendar-day fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-dark">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>@Model.PendingAppointments.Count</h4>
                                    <p class="mb-0">Lịch hẹn chờ xác nhận</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>@Model.TotalPatients</h4>
                                    <p class="mb-0">Tổng bệnh nhân</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>@Model.TotalPrescriptions</h4>
                                    <p class="mb-0">Đơn thuốc đã kê</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-prescription fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Today's Appointments -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-calendar-day"></i> Lịch hẹn hôm nay
                            </h5>
                        </div>
                        <div class="card-body">
                            @if (Model.TodayAppointments.Count > 0)
                            {
                                @foreach (var appointment in Model.TodayAppointments)
                                {
                                    <div class="border-bottom pb-2 mb-2">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <strong>@appointment.Patient.name</strong><br>
                                                <small class="text-muted">@appointment.Patient.phone</small>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge @(appointment.status == "Pending" ? "bg-warning" : appointment.status == "Confirmed" ? "bg-info" : "bg-success")">
                                                    @appointment.status
                                                </span>
                                            </div>
                                        </div>
                                        <div class="mt-1">
                                            <i class="fas fa-clock"></i> @appointment.appointment_time.ToString(@"hh\:mm")
                                            <a asp-controller="Appointments" asp-action="Details" asp-route-id="@appointment.appointment_id" 
                                               class="btn btn-sm btn-outline-primary ms-2">
                                                Chi tiết
                                            </a>
                                        </div>
                                    </div>
                                }
                            }
                            else
                            {
                                <div class="text-center text-muted">
                                    <i class="fas fa-calendar-times fa-3x mb-3"></i>
                                    <p>Không có lịch hẹn hôm nay</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <!-- Pending Appointments -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-clock"></i> Lịch hẹn chờ xác nhận
                            </h5>
                        </div>
                        <div class="card-body">
                            @if (Model.PendingAppointments.Count > 0)
                            {
                                @foreach (var appointment in Model.PendingAppointments.Take(5))
                                {
                                    <div class="border-bottom pb-2 mb-2">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <strong>@appointment.Patient.name</strong><br>
                                                <small class="text-muted">@appointment.Patient.phone</small>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge bg-warning">Chờ xác nhận</span>
                                            </div>
                                        </div>
                                        <div class="mt-1">
                                            <i class="fas fa-calendar"></i> @appointment.appointment_date.ToString("dd/MM/yyyy")
                                            <i class="fas fa-clock ms-2"></i> @appointment.appointment_time.ToString(@"hh\:mm")
                                        </div>
                                    </div>
                                }
                                <div class="text-center mt-3">
                                    <a asp-controller="Appointments" asp-action="Index" class="btn btn-outline-warning btn-sm">
                                        Xem tất cả lịch hẹn
                                    </a>
                                </div>
                            }
                            else
                            {
                                <div class="text-center text-muted">
                                    <i class="fas fa-check-circle fa-3x mb-3"></i>
                                    <p>Không có lịch hẹn chờ xác nhận</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Medical Records -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-file-medical-alt"></i> Hồ sơ bệnh án gần đây
                            </h5>
                        </div>
                        <div class="card-body">
                            @if (Model.RecentMedicalRecords.Count > 0)
                            {
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Bệnh nhân</th>
                                                <th>Chẩn đoán</th>
                                                <th>Ngày khám</th>
                                                <th>Ghi chú</th>
                                                <th>Thao tác</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var record in Model.RecentMedicalRecords)
                                            {
                                                <tr>
                                                    <td>
                                                        <strong>@record.Patient.name</strong><br>
                                                        <small class="text-muted">@record.Patient.phone</small>
                                                    </td>
                                                    <td>@record.diagnosis</td>
                                                    <td>@record.record_date.ToString("dd/MM/yyyy")</td>
                                                    <td>
                                                        @if (!string.IsNullOrEmpty(record.notes))
                                                        {
                                                            @record.notes.Substring(0, Math.Min(50, record.notes.Length))
                                                            @if (record.notes.Length > 50) { <text>...</text> }
                                                        }
                                                    </td>
                                                    <td>
                                                        <a href="#" class="btn btn-sm btn-outline-info">Chi tiết</a>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <div class="text-center text-muted">
                                    <i class="fas fa-file-medical fa-3x mb-3"></i>
                                    <p>Chưa có hồ sơ bệnh án nào</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-bolt"></i> Thao tác nhanh
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <a asp-controller="Appointments" asp-action="Index" class="btn btn-primary w-100 mb-2">
                                        <i class="fas fa-calendar-alt"></i><br>
                                        Quản lý lịch hẹn
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a asp-controller="Patients" asp-action="Index" class="btn btn-info w-100 mb-2">
                                        <i class="fas fa-users"></i><br>
                                        Danh sách bệnh nhân
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a asp-controller="Prescriptions" asp-action="Index" class="btn btn-success w-100 mb-2">
                                        <i class="fas fa-prescription"></i><br>
                                        Quản lý đơn thuốc
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a asp-controller="Prescriptions" asp-action="Create" class="btn btn-warning w-100 mb-2">
                                        <i class="fas fa-plus"></i><br>
                                        Kê đơn thuốc mới
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
