@model IEnumerable<Midterm1212.Models.ViewModels.UserManagementViewModel>

@{
    ViewData["Title"] = "Quản lý người dùng";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h2>@ViewData["Title"]</h2>
                <div>
                    <a asp-action="CreateDoctorAccount" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i> Tạo tài khoản bác sĩ
                    </a>
                    <a asp-controller="Account" asp-action="Register" class="btn btn-success">
                        <i class="fas fa-user-plus"></i> Đ<PERSON>ng ký bệnh nhân
                    </a>
                </div>
            </div>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h4>@Model.Count()</h4>
                            <p class="mb-0">Tổng người dùng</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4>@Model.Count(u => u.UserType == UserType.Patient)</h4>
                            <p class="mb-0">Bệnh nhân</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h4>@Model.Count(u => u.UserType == UserType.Doctor)</h4>
                            <p class="mb-0">Bác sĩ</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-dark">
                        <div class="card-body text-center">
                            <h4>@Model.Count(u => u.UserType == UserType.Admin)</h4>
                            <p class="mb-0">Quản trị viên</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Tên đăng nhập</th>
                                    <th>Email</th>
                                    <th>Vai trò</th>
                                    <th>Họ tên</th>
                                    <th>Trạng thái</th>
                                    <th>Ngày tạo</th>
                                    <th>Đăng nhập cuối</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var user in Model.OrderBy(u => u.UserType).ThenBy(u => u.UserName))
                                {
                                    <tr>
                                        <td>
                                            <strong>@user.UserName</strong>
                                            @if (user.UserType == UserType.Patient)
                                            {
                                                <br><small class="text-muted">Mã BN: @user.AssociatedName</small>
                                            }
                                        </td>
                                        <td>@user.Email</td>
                                        <td>
                                            @switch (user.UserType)
                                            {
                                                case UserType.Patient:
                                                    <span class="badge bg-success">Bệnh nhân</span>
                                                    break;
                                                case UserType.Doctor:
                                                    <span class="badge bg-info">Bác sĩ</span>
                                                    break;
                                                case UserType.Admin:
                                                    <span class="badge bg-danger">Quản trị viên</span>
                                                    break;
                                            }
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(user.AssociatedName))
                                            {
                                                <strong>@user.AssociatedName</strong>
                                            }
                                            else if (!string.IsNullOrEmpty(user.FullName))
                                            {
                                                <strong>@user.FullName</strong>
                                            }
                                            else
                                            {
                                                <span class="text-muted">Chưa cập nhật</span>
                                            }
                                        </td>
                                        <td>
                                            @if (user.IsActive)
                                            {
                                                <span class="badge bg-success">Hoạt động</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">Tạm khóa</span>
                                            }
                                        </td>
                                        <td>@user.CreatedDate.ToString("dd/MM/yyyy")</td>
                                        <td>
                                            @if (user.LastLoginDate.HasValue)
                                            {
                                                @user.LastLoginDate.Value.ToString("dd/MM/yyyy HH:mm")
                                            }
                                            else
                                            {
                                                <span class="text-muted">Chưa đăng nhập</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                @if (user.UserType == UserType.Patient)
                                                {
                                                    <a asp-controller="Patients" asp-action="Details" asp-route-id="@user.AssociatedName" 
                                                       class="btn btn-outline-info btn-sm" title="Xem hồ sơ bệnh nhân">
                                                        <i class="fas fa-user"></i>
                                                    </a>
                                                }
                                                else if (user.UserType == UserType.Doctor)
                                                {
                                                    <a asp-controller="Doctor" asp-action="Details" asp-route-id="@user.AssociatedName" 
                                                       class="btn btn-outline-info btn-sm" title="Xem hồ sơ bác sĩ">
                                                        <i class="fas fa-user-md"></i>
                                                    </a>
                                                }
                                                
                                                @if (user.UserType != UserType.Admin)
                                                {
                                                    @if (user.IsActive)
                                                    {
                                                        <button class="btn btn-outline-warning btn-sm" title="Tạm khóa tài khoản"
                                                                onclick="toggleUserStatus('@user.Id', false)">
                                                            <i class="fas fa-lock"></i>
                                                        </button>
                                                    }
                                                    else
                                                    {
                                                        <button class="btn btn-outline-success btn-sm" title="Kích hoạt tài khoản"
                                                                onclick="toggleUserStatus('@user.Id', true)">
                                                            <i class="fas fa-unlock"></i>
                                                        </button>
                                                    }
                                                }
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Người dùng mới nhất</h6>
                        </div>
                        <div class="card-body">
                            @foreach (var user in Model.OrderByDescending(u => u.CreatedDate).Take(5))
                            {
                                <div class="border-bottom pb-2 mb-2">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <strong>@user.UserName</strong>
                                            <span class="badge bg-@(user.UserType == UserType.Patient ? "success" : user.UserType == UserType.Doctor ? "info" : "danger") ms-2">
                                                @user.UserType.ToString()
                                            </span>
                                        </div>
                                        <small class="text-muted">@user.CreatedDate.ToString("dd/MM/yyyy")</small>
                                    </div>
                                    @if (!string.IsNullOrEmpty(user.AssociatedName))
                                    {
                                        <small class="text-muted">@user.AssociatedName</small>
                                    }
                                </div>
                            }
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Đăng nhập gần đây</h6>
                        </div>
                        <div class="card-body">
                            @foreach (var user in Model.Where(u => u.LastLoginDate.HasValue).OrderByDescending(u => u.LastLoginDate).Take(5))
                            {
                                <div class="border-bottom pb-2 mb-2">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <strong>@user.UserName</strong>
                                            <span class="badge bg-@(user.UserType == UserType.Patient ? "success" : user.UserType == UserType.Doctor ? "info" : "danger") ms-2">
                                                @user.UserType.ToString()
                                            </span>
                                        </div>
                                        <small class="text-muted">@user.LastLoginDate.Value.ToString("dd/MM/yyyy HH:mm")</small>
                                    </div>
                                    @if (!string.IsNullOrEmpty(user.AssociatedName))
                                    {
                                        <small class="text-muted">@user.AssociatedName</small>
                                    }
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function toggleUserStatus(userId, isActive) {
            const action = isActive ? 'kích hoạt' : 'tạm khóa';
            if (confirm(`Bạn có chắc chắn muốn ${action} tài khoản này?`)) {
                // Implementation for toggling user status
                // This would typically be a POST request to update user status
                alert(`Chức năng ${action} tài khoản sẽ được triển khai trong phiên bản tiếp theo.`);
            }
        }
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    </script>
}
