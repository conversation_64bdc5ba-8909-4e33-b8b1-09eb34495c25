using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Midterm1212.Models;

namespace Midterm1212.Controllers
{
    public class MedicationsController : Controller
    {
        private readonly MedicalDbContext _context;

        public MedicationsController(MedicalDbContext context)
        {
            _context = context;
        }

        // GET: Medications
        public async Task<IActionResult> Index(string searchString, int? categoryId)
        {
            var medications = _context.Medications.Include(m => m.Category).AsQueryable();

            if (!string.IsNullOrEmpty(searchString))
            {
                medications = medications.Where(m => m.medication_name.Contains(searchString) ||
                                                   m.active_ingredient.Contains(searchString) ||
                                                   m.manufacturer.Contains(searchString));
            }

            if (categoryId.HasValue)
            {
                medications = medications.Where(m => m.category_id == categoryId.Value);
            }

            ViewData["Categories"] = new SelectList(await _context.MedicationCategories.ToListAsync(), "category_id", "category_name");
            ViewData["CurrentFilter"] = searchString;
            ViewData["CurrentCategory"] = categoryId;

            return View(await medications.ToListAsync());
        }

        // GET: Medications/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var medication = await _context.Medications
                .Include(m => m.Category)
                .Include(m => m.InventoryAlerts)
                .FirstOrDefaultAsync(m => m.medication_id == id);
            if (medication == null)
            {
                return NotFound();
            }

            return View(medication);
        }

        // GET: Medications/Create
        public IActionResult Create()
        {
            ViewData["category_id"] = new SelectList(_context.MedicationCategories, "category_id", "category_name");
            return View();
        }

        // POST: Medications/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("medication_id,medication_name,active_ingredient,dosage,form,manufacturer,stock_quantity,minimum_stock,unit_price,manufacture_date,expiry_date,category_id,notes")] Medication medication)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    foreach (var error in ModelState)
                    {
                        foreach (var subError in error.Value.Errors)
                        {
                            ModelState.AddModelError("", $"{error.Key}: {subError.ErrorMessage}");
                        }
                    }
                    ViewData["category_id"] = new SelectList(_context.MedicationCategories, "category_id", "category_name", medication.category_id);
                    return View(medication);
                }

                medication.created_date = DateTime.Now;
                _context.Add(medication);
                await _context.SaveChangesAsync();

                // Check for low stock alert
                await CheckAndCreateInventoryAlert(medication);

                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "Đã xảy ra lỗi khi lưu: " + ex.Message);
                ViewData["category_id"] = new SelectList(_context.MedicationCategories, "category_id", "category_name", medication.category_id);
                return View(medication);
            }
        }

        // GET: Medications/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var medication = await _context.Medications.FindAsync(id);
            if (medication == null)
            {
                return NotFound();
            }
            ViewData["category_id"] = new SelectList(_context.MedicationCategories, "category_id", "category_name", medication.category_id);
            return View(medication);
        }

        // POST: Medications/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("medication_id,medication_name,active_ingredient,dosage,form,manufacturer,stock_quantity,minimum_stock,unit_price,manufacture_date,expiry_date,category_id,notes,created_date")] Medication medication)
        {
            if (id != medication.medication_id)
            {
                return NotFound();
            }

            try
            {
                if (!ModelState.IsValid)
                {
                    foreach (var error in ModelState)
                    {
                        foreach (var subError in error.Value.Errors)
                        {
                            ModelState.AddModelError("", $"{error.Key}: {subError.ErrorMessage}");
                        }
                    }
                    ViewData["category_id"] = new SelectList(_context.MedicationCategories, "category_id", "category_name", medication.category_id);
                    return View(medication);
                }

                medication.updated_date = DateTime.Now;
                _context.Update(medication);
                await _context.SaveChangesAsync();

                // Check for inventory alerts after update
                await CheckAndCreateInventoryAlert(medication);

                return RedirectToAction(nameof(Index));
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!MedicationExists(medication.medication_id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "Đã xảy ra lỗi khi cập nhật: " + ex.Message);
                ViewData["category_id"] = new SelectList(_context.MedicationCategories, "category_id", "category_name", medication.category_id);
                return View(medication);
            }
        }

        // GET: Medications/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var medication = await _context.Medications
                .Include(m => m.Category)
                .FirstOrDefaultAsync(m => m.medication_id == id);
            if (medication == null)
            {
                return NotFound();
            }

            return View(medication);
        }

        // POST: Medications/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var medication = await _context.Medications.FindAsync(id);
            if (medication != null)
            {
                _context.Medications.Remove(medication);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        // GET: Medications/LowStock
        public async Task<IActionResult> LowStock()
        {
            var lowStockMedications = await _context.Medications
                .Include(m => m.Category)
                .Where(m => m.stock_quantity <= m.minimum_stock)
                .ToListAsync();

            return View(lowStockMedications);
        }

        // GET: Medications/ExpiringSoon
        public async Task<IActionResult> ExpiringSoon()
        {
            var expiringMedications = await _context.Medications
                .Include(m => m.Category)
                .Where(m => m.expiry_date <= DateTime.Now.AddDays(30))
                .OrderBy(m => m.expiry_date)
                .ToListAsync();

            return View(expiringMedications);
        }

        private bool MedicationExists(int id)
        {
            return _context.Medications.Any(e => e.medication_id == id);
        }

        private async Task CheckAndCreateInventoryAlert(Medication medication)
        {
            var alerts = new List<InventoryAlert>();

            // Check for low stock
            if (medication.stock_quantity <= medication.minimum_stock)
            {
                var existingAlert = await _context.InventoryAlerts
                    .FirstOrDefaultAsync(a => a.medication_id == medication.medication_id && 
                                            a.alert_type == "Tồn kho thấp" && 
                                            a.status == "Chưa xử lý");

                if (existingAlert == null)
                {
                    alerts.Add(new InventoryAlert
                    {
                        alert_id = await GetNextAlertId(),
                        medication_id = medication.medication_id,
                        alert_type = "Tồn kho thấp",
                        priority_level = medication.stock_quantity == 0 ? "Khẩn cấp" : "Cao",
                        message = $"Thuốc {medication.medication_name} có số lượng tồn kho thấp ({medication.stock_quantity} còn lại)",
                        status = "Chưa xử lý",
                        created_date = DateTime.Now
                    });
                }
            }

            // Check for expiring soon
            if (medication.expiry_date <= DateTime.Now.AddDays(30))
            {
                var existingAlert = await _context.InventoryAlerts
                    .FirstOrDefaultAsync(a => a.medication_id == medication.medication_id && 
                                            a.alert_type == "Hết hạn" && 
                                            a.status == "Chưa xử lý");

                if (existingAlert == null)
                {
                    alerts.Add(new InventoryAlert
                    {
                        alert_id = await GetNextAlertId(),
                        medication_id = medication.medication_id,
                        alert_type = "Hết hạn",
                        priority_level = medication.expiry_date <= DateTime.Now.AddDays(7) ? "Khẩn cấp" : "Cao",
                        message = $"Thuốc {medication.medication_name} sắp hết hạn (hết hạn: {medication.expiry_date:dd/MM/yyyy})",
                        status = "Chưa xử lý",
                        created_date = DateTime.Now
                    });
                }
            }

            if (alerts.Any())
            {
                _context.InventoryAlerts.AddRange(alerts);
                await _context.SaveChangesAsync();
            }
        }

        private async Task<int> GetNextAlertId()
        {
            var lastAlert = await _context.InventoryAlerts.OrderByDescending(a => a.alert_id).FirstOrDefaultAsync();
            return lastAlert?.alert_id + 1 ?? 1;
        }
    }
}
