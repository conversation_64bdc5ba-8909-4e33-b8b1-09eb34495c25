using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace Midterm1212.Models
{
    public class MedicalDbContext : IdentityDbContext
    {
        public MedicalDbContext(DbContextOptions<MedicalDbContext> options)
            : base(options)
        {
        }

        public DbSet<Patient> Patients { get; set; }
        public DbSet<Doctor> Doctors { get; set; }
        public DbSet<Appointment> Appointments { get; set; }
        public DbSet<MedicalRecord> MedicalRecords { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            builder.Entity<Patient>(entity =>
            {
                entity.HasKey(e => e.patient_id);
                entity.Property(e => e.patient_id).ValueGeneratedNever();
                entity.Property(e => e.name).IsRequired();
                entity.Property(e => e.gender).IsRequired();
                entity.Property(e => e.phone).IsRequired();
                entity.Property(e => e.address).IsRequired();
            });

            builder.Entity<Doctor>(entity =>
            {
                entity.HasKey(e => e.doctor_id);
                entity.Property(e => e.doctor_id).ValueGeneratedNever();
                entity.Property(e => e.name).IsRequired();
                entity.Property(e => e.specialization).IsRequired();
                entity.Property(e => e.phone).IsRequired();
                entity.Property(e => e.email).IsRequired();
                entity.Property(e => e.work_experience).IsRequired();
            });

            builder.Entity<Appointment>(entity =>
            {
                entity.HasKey(e => e.appointment_id);
                entity.Property(e => e.appointment_id).ValueGeneratedNever();
                entity.Property(e => e.status).IsRequired();

                entity.HasOne(d => d.Patient)
                    .WithMany(p => p.Appointments)
                    .HasForeignKey(d => d.patient_id)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(d => d.Doctor)
                    .WithMany(p => p.Appointments)
                    .HasForeignKey(d => d.doctor_id)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            builder.Entity<MedicalRecord>(entity =>
            {
                entity.HasKey(e => e.record_id);
                entity.Property(e => e.record_id).ValueGeneratedNever();
                entity.Property(e => e.diagnosis).IsRequired();
                entity.Property(e => e.prescription).IsRequired();

                entity.HasOne(d => d.Patient)
                    .WithMany(p => p.MedicalRecords)
                    .HasForeignKey(d => d.patient_id)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(d => d.Doctor)
                    .WithMany(p => p.MedicalRecords)
                    .HasForeignKey(d => d.doctor_id)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(d => d.Appointment)
                    .WithMany(p => p.MedicalRecords)
                    .HasForeignKey(d => d.appointment_id)
                    .OnDelete(DeleteBehavior.Restrict);
            });
        }
    }
}
