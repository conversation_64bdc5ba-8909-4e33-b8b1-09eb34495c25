using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace Midterm1212.Models
{
    public class MedicalDbContext : IdentityDbContext
    {
        public MedicalDbContext(DbContextOptions<MedicalDbContext> options)
            : base(options)
        {
        }

        public DbSet<Patient> Patients { get; set; }
        public DbSet<Doctor> Doctors { get; set; }
        public DbSet<Appointment> Appointments { get; set; }
        public DbSet<MedicalRecord> MedicalRecords { get; set; }
        public DbSet<Medicine> Medicines { get; set; }
        public DbSet<Prescription> Prescriptions { get; set; }
        public DbSet<PrescriptionDetail> PrescriptionDetails { get; set; }
        public DbSet<MedicationOrder> MedicationOrders { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            builder.Entity<Patient>(entity =>
            {
                entity.HasKey(e => e.patient_id);
                entity.Property(e => e.patient_id).ValueGeneratedNever();
                entity.Property(e => e.name).IsRequired();
                entity.Property(e => e.gender).IsRequired();
                entity.Property(e => e.phone).IsRequired();
                entity.Property(e => e.address).IsRequired();
            });

            builder.Entity<Doctor>(entity =>
            {
                entity.HasKey(e => e.doctor_id);
                entity.Property(e => e.doctor_id).ValueGeneratedNever();
                entity.Property(e => e.name).IsRequired();
                entity.Property(e => e.specialization).IsRequired();
                entity.Property(e => e.phone).IsRequired();
                entity.Property(e => e.email).IsRequired();
                entity.Property(e => e.work_experience).IsRequired();
            });

            builder.Entity<Appointment>(entity =>
            {
                entity.HasKey(e => e.appointment_id);
                entity.Property(e => e.appointment_id).ValueGeneratedNever();
                entity.Property(e => e.status).IsRequired();

                entity.HasOne(d => d.Patient)
                    .WithMany(p => p.Appointments)
                    .HasForeignKey(d => d.patient_id)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(d => d.Doctor)
                    .WithMany(p => p.Appointments)
                    .HasForeignKey(d => d.doctor_id)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            builder.Entity<MedicalRecord>(entity =>
            {
                entity.HasKey(e => e.record_id);
                entity.Property(e => e.record_id).ValueGeneratedNever();
                entity.Property(e => e.diagnosis).IsRequired();
                entity.Property(e => e.prescription).IsRequired();

                entity.HasOne(d => d.Patient)
                    .WithMany(p => p.MedicalRecords)
                    .HasForeignKey(d => d.patient_id)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(d => d.Doctor)
                    .WithMany(p => p.MedicalRecords)
                    .HasForeignKey(d => d.doctor_id)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(d => d.Appointment)
                    .WithMany(p => p.MedicalRecords)
                    .HasForeignKey(d => d.appointment_id)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            builder.Entity<Medicine>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedNever();
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.Description).IsRequired();
                entity.Property(e => e.Price).HasColumnType("decimal(10,2)");
            });

            builder.Entity<Prescription>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedNever();
                entity.Property(e => e.TotalCost).HasColumnType("decimal(12,2)");
                entity.Property(e => e.Status).IsRequired();

                entity.HasOne(d => d.MedicalRecord)
                    .WithMany(p => p.Prescriptions)
                    .HasForeignKey(d => d.MedicalRecordId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            builder.Entity<PrescriptionDetail>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedNever();
                entity.Property(e => e.Dosage).IsRequired();
                entity.Property(e => e.UnitPrice).HasColumnType("decimal(10,2)");
                entity.Property(e => e.TotalPrice).HasColumnType("decimal(12,2)");

                entity.HasOne(d => d.Prescription)
                    .WithMany(p => p.PrescriptionDetails)
                    .HasForeignKey(d => d.PrescriptionId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.Medicine)
                    .WithMany(p => p.PrescriptionDetails)
                    .HasForeignKey(d => d.MedicineId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            builder.Entity<MedicationOrder>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedNever();
                entity.Property(e => e.TotalAmount).HasColumnType("decimal(12,2)");
                entity.Property(e => e.PaymentMethod).IsRequired();
                entity.Property(e => e.OrderStatus).IsRequired();
                entity.Property(e => e.DeliveryAddress).IsRequired();
                entity.Property(e => e.ContactPhone).IsRequired();

                entity.HasOne(d => d.Patient)
                    .WithMany(p => p.MedicationOrders)
                    .HasForeignKey(d => d.PatientId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(d => d.Prescription)
                    .WithMany(p => p.MedicationOrders)
                    .HasForeignKey(d => d.PrescriptionId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
        }
    }
}
