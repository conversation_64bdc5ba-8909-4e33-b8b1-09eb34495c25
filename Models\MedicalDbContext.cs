using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace Midterm1212.Models
{
    public class MedicalDbContext : IdentityDbContext
    {
        public MedicalDbContext(DbContextOptions<MedicalDbContext> options)
            : base(options)
        {
        }

        public DbSet<Patient> Patients { get; set; }
        public DbSet<Doctor> Doctors { get; set; }
        public DbSet<Appointment> Appointments { get; set; }
        public DbSet<MedicalRecord> MedicalRecords { get; set; }
        public DbSet<MedicationCategory> MedicationCategories { get; set; }
        public DbSet<Medication> Medications { get; set; }
        public DbSet<Prescription> Prescriptions { get; set; }
        public DbSet<PrescriptionItem> PrescriptionItems { get; set; }
        public DbSet<MedicationOrder> MedicationOrders { get; set; }
        public DbSet<InventoryAlert> InventoryAlerts { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            builder.Entity<Patient>(entity =>
            {
                entity.HasKey(e => e.patient_id);
                entity.Property(e => e.patient_id).ValueGeneratedNever();
                entity.Property(e => e.name).IsRequired();
                entity.Property(e => e.gender).IsRequired();
                entity.Property(e => e.phone).IsRequired();
                entity.Property(e => e.address).IsRequired();
            });

            builder.Entity<Doctor>(entity =>
            {
                entity.HasKey(e => e.doctor_id);
                entity.Property(e => e.doctor_id).ValueGeneratedNever();
                entity.Property(e => e.name).IsRequired();
                entity.Property(e => e.specialization).IsRequired();
                entity.Property(e => e.phone).IsRequired();
                entity.Property(e => e.email).IsRequired();
                entity.Property(e => e.work_experience).IsRequired();
            });

            builder.Entity<Appointment>(entity =>
            {
                entity.HasKey(e => e.appointment_id);
                entity.Property(e => e.appointment_id).ValueGeneratedNever();
                entity.Property(e => e.status).IsRequired();

                entity.HasOne(d => d.Patient)
                    .WithMany(p => p.Appointments)
                    .HasForeignKey(d => d.patient_id)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(d => d.Doctor)
                    .WithMany(p => p.Appointments)
                    .HasForeignKey(d => d.doctor_id)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            builder.Entity<MedicalRecord>(entity =>
            {
                entity.HasKey(e => e.record_id);
                entity.Property(e => e.record_id).ValueGeneratedNever();
                entity.Property(e => e.diagnosis).IsRequired();
                entity.Property(e => e.prescription).IsRequired();

                entity.HasOne(d => d.Patient)
                    .WithMany(p => p.MedicalRecords)
                    .HasForeignKey(d => d.patient_id)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(d => d.Doctor)
                    .WithMany(p => p.MedicalRecords)
                    .HasForeignKey(d => d.doctor_id)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(d => d.Appointment)
                    .WithMany(p => p.MedicalRecords)
                    .HasForeignKey(d => d.appointment_id)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            builder.Entity<MedicationCategory>(entity =>
            {
                entity.HasKey(e => e.category_id);
                entity.Property(e => e.category_id).ValueGeneratedNever();
                entity.Property(e => e.category_name).IsRequired();
                entity.Property(e => e.created_date).IsRequired();
            });

            builder.Entity<Medication>(entity =>
            {
                entity.HasKey(e => e.medication_id);
                entity.Property(e => e.medication_id).ValueGeneratedNever();
                entity.Property(e => e.medication_name).IsRequired();
                entity.Property(e => e.active_ingredient).IsRequired();
                entity.Property(e => e.dosage).IsRequired();
                entity.Property(e => e.form).IsRequired();
                entity.Property(e => e.manufacturer).IsRequired();
                entity.Property(e => e.stock_quantity).IsRequired();
                entity.Property(e => e.minimum_stock).IsRequired();
                entity.Property(e => e.unit_price).IsRequired().HasColumnType("decimal(10,2)");
                entity.Property(e => e.manufacture_date).IsRequired();
                entity.Property(e => e.expiry_date).IsRequired();
                entity.Property(e => e.created_date).IsRequired();

                entity.HasOne(d => d.Category)
                    .WithMany(p => p.Medications)
                    .HasForeignKey(d => d.category_id)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            builder.Entity<Prescription>(entity =>
            {
                entity.HasKey(e => e.prescription_id);
                entity.Property(e => e.prescription_id).ValueGeneratedNever();
                entity.Property(e => e.prescription_date).IsRequired();
                entity.Property(e => e.status).IsRequired();
                entity.Property(e => e.total_amount).IsRequired().HasColumnType("decimal(12,2)");
                entity.Property(e => e.created_date).IsRequired();

                entity.HasOne(d => d.Patient)
                    .WithMany(p => p.Prescriptions)
                    .HasForeignKey(d => d.patient_id)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(d => d.Doctor)
                    .WithMany(p => p.Prescriptions)
                    .HasForeignKey(d => d.doctor_id)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(d => d.MedicalRecord)
                    .WithMany(p => p.Prescriptions)
                    .HasForeignKey(d => d.medical_record_id)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            builder.Entity<PrescriptionItem>(entity =>
            {
                entity.HasKey(e => e.prescription_item_id);
                entity.Property(e => e.prescription_item_id).ValueGeneratedNever();
                entity.Property(e => e.quantity).IsRequired();
                entity.Property(e => e.dosage_instructions).IsRequired();
                entity.Property(e => e.frequency).IsRequired();
                entity.Property(e => e.duration).IsRequired();
                entity.Property(e => e.unit_price).IsRequired().HasColumnType("decimal(10,2)");
                entity.Property(e => e.total_price).IsRequired().HasColumnType("decimal(12,2)");

                entity.HasOne(d => d.Prescription)
                    .WithMany(p => p.PrescriptionItems)
                    .HasForeignKey(d => d.prescription_id)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.Medication)
                    .WithMany(p => p.PrescriptionItems)
                    .HasForeignKey(d => d.medication_id)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            builder.Entity<MedicationOrder>(entity =>
            {
                entity.HasKey(e => e.order_id);
                entity.Property(e => e.order_id).ValueGeneratedNever();
                entity.Property(e => e.order_date).IsRequired();
                entity.Property(e => e.order_status).IsRequired();
                entity.Property(e => e.payment_method).IsRequired();
                entity.Property(e => e.payment_status).IsRequired();
                entity.Property(e => e.total_amount).IsRequired().HasColumnType("decimal(12,2)");
                entity.Property(e => e.shipping_fee).HasColumnType("decimal(12,2)");
                entity.Property(e => e.final_amount).IsRequired().HasColumnType("decimal(12,2)");
                entity.Property(e => e.delivery_address).IsRequired();
                entity.Property(e => e.contact_phone).IsRequired();
                entity.Property(e => e.created_date).IsRequired();

                entity.HasOne(d => d.Prescription)
                    .WithMany(p => p.MedicationOrders)
                    .HasForeignKey(d => d.prescription_id)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(d => d.Patient)
                    .WithMany(p => p.MedicationOrders)
                    .HasForeignKey(d => d.patient_id)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            builder.Entity<InventoryAlert>(entity =>
            {
                entity.HasKey(e => e.alert_id);
                entity.Property(e => e.alert_id).ValueGeneratedNever();
                entity.Property(e => e.alert_type).IsRequired();
                entity.Property(e => e.priority_level).IsRequired();
                entity.Property(e => e.message).IsRequired();
                entity.Property(e => e.status).IsRequired();
                entity.Property(e => e.created_date).IsRequired();

                entity.HasOne(d => d.Medication)
                    .WithMany(p => p.InventoryAlerts)
                    .HasForeignKey(d => d.medication_id)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }
    }
}
