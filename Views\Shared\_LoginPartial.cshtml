﻿@using Microsoft.AspNetCore.Identity
@using Midterm1212.Models

@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> UserManager

<ul class="navbar-nav">
@if (SignInManager.IsSignedIn(User))
{
    <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle text-light" href="#" role="button" data-bs-toggle="dropdown">
            <i class="fas fa-user"></i> @UserManager.GetUserName(User)
            @{
                var currentUser = await UserManager.GetUserAsync(User);
                if (currentUser != null)
                {
                    <span class="badge bg-secondary">@currentUser.UserType.ToString()</span>
                }
            }
        </a>
        <ul class="dropdown-menu">
            @if (User.IsInRole("Patient"))
            {
                <li><a class="dropdown-item" asp-controller="My" asp-action="Profile">
                    <i class="fas fa-user"></i> <PERSON><PERSON> sơ cá nhân
                </a></li>
            }
            <li><a class="dropdown-item" asp-controller="Account" asp-action="ChangePassword">
                <i class="fas fa-key"></i> Đổi mật khẩu
            </a></li>
            <li><hr class="dropdown-divider"></li>
            <li>
                <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline">
                    <button type="submit" class="dropdown-item">
                        <i class="fas fa-sign-out-alt"></i> Đăng xuất
                    </button>
                </form>
            </li>
        </ul>
    </li>
}
else
{
    <li class="nav-item">
        <a class="nav-link text-light" asp-controller="Account" asp-action="Register">
            <i class="fas fa-user-plus"></i> Đăng ký
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link text-light" asp-controller="Account" asp-action="Login">
            <i class="fas fa-sign-in-alt"></i> Đăng nhập
        </a>
    </li>
}
</ul>
