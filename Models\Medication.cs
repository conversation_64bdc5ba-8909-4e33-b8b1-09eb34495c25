using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Midterm1212.Models
{
    public class Medication
    {
        public Medication()
        {
            PrescriptionItems = new HashSet<PrescriptionItem>();
            InventoryAlerts = new HashSet<InventoryAlert>();
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        [Required]
        public int medication_id { get; set; }

        [Required]
        [Display(Name = "Tên thuốc")]
        public string medication_name { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Thành phần hoạt chất")]
        public string active_ingredient { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Liều lượng")]
        public string dosage { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Dạng thuốc")]
        public string form { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Nhà sản xuất")]
        public string manufacturer { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Số lượng tồn kho")]
        public int stock_quantity { get; set; }

        [Required]
        [Display(Name = "Số lượng tối thiểu")]
        public int minimum_stock { get; set; }

        [Required]
        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "Giá bán")]
        public decimal unit_price { get; set; }

        [Required]
        [Display(Name = "Ngày sản xuất")]
        [DataType(DataType.Date)]
        public DateTime manufacture_date { get; set; }

        [Required]
        [Display(Name = "Ngày hết hạn")]
        [DataType(DataType.Date)]
        public DateTime expiry_date { get; set; }

        [Required]
        [Display(Name = "Danh mục")]
        public int category_id { get; set; }

        [Display(Name = "Ghi chú")]
        public string? notes { get; set; }

        [Required]
        [Display(Name = "Ngày tạo")]
        public DateTime created_date { get; set; } = DateTime.Now;

        [Display(Name = "Ngày cập nhật")]
        public DateTime? updated_date { get; set; }

        [ForeignKey("category_id")]
        public virtual MedicationCategory Category { get; set; }

        public virtual ICollection<PrescriptionItem> PrescriptionItems { get; set; }
        public virtual ICollection<InventoryAlert> InventoryAlerts { get; set; }
    }
}
