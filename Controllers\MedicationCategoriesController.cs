using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Midterm1212.Models;

namespace Midterm1212.Controllers
{
    public class MedicationCategoriesController : Controller
    {
        private readonly MedicalDbContext _context;

        public MedicationCategoriesController(MedicalDbContext context)
        {
            _context = context;
        }

        // GET: MedicationCategories
        public async Task<IActionResult> Index()
        {
            var categories = await _context.MedicationCategories
                .Include(c => c.Medications)
                .ToListAsync();
            return View(categories);
        }

        // GET: MedicationCategories/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var medicationCategory = await _context.MedicationCategories
                .Include(c => c.Medications)
                .FirstOrDefaultAsync(m => m.category_id == id);
            if (medicationCategory == null)
            {
                return NotFound();
            }

            return View(medicationCategory);
        }

        // GET: MedicationCategories/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: MedicationCategories/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("category_id,category_name,description")] MedicationCategory medicationCategory)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    foreach (var error in ModelState)
                    {
                        foreach (var subError in error.Value.Errors)
                        {
                            ModelState.AddModelError("", $"{error.Key}: {subError.ErrorMessage}");
                        }
                    }
                    return View(medicationCategory);
                }

                medicationCategory.created_date = DateTime.Now;
                _context.Add(medicationCategory);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "Đã xảy ra lỗi khi lưu: " + ex.Message);
                return View(medicationCategory);
            }
        }

        // GET: MedicationCategories/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var medicationCategory = await _context.MedicationCategories.FindAsync(id);
            if (medicationCategory == null)
            {
                return NotFound();
            }
            return View(medicationCategory);
        }

        // POST: MedicationCategories/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("category_id,category_name,description,created_date")] MedicationCategory medicationCategory)
        {
            if (id != medicationCategory.category_id)
            {
                return NotFound();
            }

            try
            {
                if (!ModelState.IsValid)
                {
                    foreach (var error in ModelState)
                    {
                        foreach (var subError in error.Value.Errors)
                        {
                            ModelState.AddModelError("", $"{error.Key}: {subError.ErrorMessage}");
                        }
                    }
                    return View(medicationCategory);
                }

                _context.Update(medicationCategory);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!MedicationCategoryExists(medicationCategory.category_id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "Đã xảy ra lỗi khi cập nhật: " + ex.Message);
                return View(medicationCategory);
            }
        }

        // GET: MedicationCategories/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var medicationCategory = await _context.MedicationCategories
                .Include(c => c.Medications)
                .FirstOrDefaultAsync(m => m.category_id == id);
            if (medicationCategory == null)
            {
                return NotFound();
            }

            return View(medicationCategory);
        }

        // POST: MedicationCategories/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var medicationCategory = await _context.MedicationCategories
                .Include(c => c.Medications)
                .FirstOrDefaultAsync(c => c.category_id == id);

            if (medicationCategory != null)
            {
                if (medicationCategory.Medications.Any())
                {
                    ModelState.AddModelError("", "Không thể xóa danh mục này vì vẫn còn thuốc thuộc danh mục này.");
                    return View("Delete", medicationCategory);
                }

                _context.MedicationCategories.Remove(medicationCategory);
                await _context.SaveChangesAsync();
            }

            return RedirectToAction(nameof(Index));
        }

        private bool MedicationCategoryExists(int id)
        {
            return _context.MedicationCategories.Any(e => e.category_id == id);
        }
    }
}
