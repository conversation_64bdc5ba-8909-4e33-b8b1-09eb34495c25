﻿@model Midterm1212.Models.Appointment

@{
    ViewData["Title"] = "Chi tiết lịch hẹn";
}

<div class="container">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-calendar-alt"></i> @ViewData["Title"] #@Model.appointment_id
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Thông tin bệnh nhân:</h6>
                            <dl class="row">
                                <dt class="col-sm-5">Mã bệnh nhân:</dt>
                                <dd class="col-sm-7">#@Model.Patient.patient_id</dd>

                                <dt class="col-sm-5">Họ và tên:</dt>
                                <dd class="col-sm-7"><strong>@Model.Patient.name</strong></dd>

                                <dt class="col-sm-5">Số điện thoại:</dt>
                                <dd class="col-sm-7">@Model.Patient.phone</dd>

                                <dt class="col-sm-5">Địa chỉ:</dt>
                                <dd class="col-sm-7">@Model.Patient.address</dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <h6>Thông tin bác sĩ:</h6>
                            <dl class="row">
                                <dt class="col-sm-5">Mã bác sĩ:</dt>
                                <dd class="col-sm-7">#@Model.Doctor.doctor_id</dd>

                                <dt class="col-sm-5">Họ và tên:</dt>
                                <dd class="col-sm-7"><strong>BS. @Model.Doctor.name</strong></dd>

                                <dt class="col-sm-5">Chuyên khoa:</dt>
                                <dd class="col-sm-7">@Model.Doctor.specialization</dd>

                                <dt class="col-sm-5">Email:</dt>
                                <dd class="col-sm-7">@Model.Doctor.email</dd>
                            </dl>
                        </div>
                    </div>

                    <hr>

                    <div class="row">
                        <div class="col-md-12">
                            <h6>Thông tin lịch hẹn:</h6>
                            <dl class="row">
                                <dt class="col-sm-3">Ngày hẹn:</dt>
                                <dd class="col-sm-3"><strong>@Model.appointment_date.ToString("dd/MM/yyyy")</strong></dd>

                                <dt class="col-sm-3">Giờ hẹn:</dt>
                                <dd class="col-sm-3"><strong>@Model.appointment_time.ToString(@"hh\:mm")</strong></dd>
                            </dl>

                            <dl class="row">
                                <dt class="col-sm-3">Trạng thái:</dt>
                                <dd class="col-sm-9">
                                    @switch (Model.status)
                                    {
                                        case "Pending":
                                            <span class="badge bg-warning fs-6">Chờ xác nhận</span>
                                            break;
                                        case "Confirmed":
                                            <span class="badge bg-info fs-6">Đã xác nhận</span>
                                            break;
                                        case "Complete":
                                            <span class="badge bg-success fs-6">Hoàn thành</span>
                                            break;
                                        case "Cancelled":
                                            <span class="badge bg-danger fs-6">Đã hủy</span>
                                            break;
                                        default:
                                            <span class="badge bg-secondary fs-6">@Model.status</span>
                                            break;
                                    }
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại danh sách
                        </a>
                        <div>
                            @if (User.IsInRole("Admin") || User.IsInRole("Doctor"))
                            {
                                <a asp-action="Edit" asp-route-id="@Model.appointment_id" class="btn btn-primary">
                                    <i class="fas fa-edit"></i> Chỉnh sửa
                                </a>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Thao tác nhanh</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a asp-controller="Patients" asp-action="Details" asp-route-id="@Model.Patient.patient_id" class="btn btn-outline-info">
                            <i class="fas fa-user"></i> Xem hồ sơ bệnh nhân
                        </a>
                        <a asp-controller="Doctor" asp-action="Details" asp-route-id="@Model.Doctor.doctor_id" class="btn btn-outline-info">
                            <i class="fas fa-user-md"></i> Xem hồ sơ bác sĩ
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
