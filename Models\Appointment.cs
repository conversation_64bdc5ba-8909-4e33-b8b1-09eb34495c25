using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Midterm1212.Models
{
    public class Appointment
    {
        public Appointment()
        {
            MedicalRecords = new HashSet<MedicalRecord>();
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        [Required(ErrorMessage = "Mã lịch hẹn là bắt buộc")]
        [Display(Name = "Mã lịch hẹn")]
        public int appointment_id { get; set; }

        [Required(ErrorMessage = "Vui lòng chọn bệnh nhân")]
        [Display(Name = "Bệnh nhân")]
        public int patient_id { get; set; }

        [Required(ErrorMessage = "Vui lòng chọn bác sĩ")]
        [Display(Name = "<PERSON>ác sĩ")]
        public int doctor_id { get; set; }

        [Required(ErrorMessage = "Vui lòng chọn ngày hẹn")]
        [Display(Name = "Ngày hẹn")]
        [DataType(DataType.Date)]
        public DateTime appointment_date { get; set; }

        [Required(ErrorMessage = "Vui lòng chọn giờ hẹn")]
        [Display(Name = "Giờ hẹn")]
        [DataType(DataType.Time)]
        public TimeSpan appointment_time { get; set; }

        [Required(ErrorMessage = "Trạng thái là bắt buộc")]
        [Display(Name = "Trạng thái")]
        public string status { get; set; } = string.Empty;

        [ForeignKey("patient_id")]
        public virtual Patient Patient { get; set; }

        [ForeignKey("doctor_id")]
        public virtual Doctor Doctor { get; set; }

        public virtual ICollection<MedicalRecord> MedicalRecords { get; set; }
    }
}
