@model Midterm1212.Models.ViewModels.RegisterPatientViewModel

@{
    ViewData["Title"] = "Đăng ký bệnh nhân";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-success text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-user-plus"></i> @ViewData["Title"]
                    </h4>
                </div>
                <div class="card-body">
                    <form asp-action="Register" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="PatientId" class="form-label"></label>
                                    <input asp-for="PatientId" class="form-control" readonly />
                                    <span asp-validation-for="PatientId" class="text-danger"></span>
                                    <div class="form-text">M<PERSON> bệnh nhân được tạo tự động</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="UserName" class="form-label"></label>
                                    <input asp-for="UserName" class="form-control" placeholder="Nhập tên đăng nhập" />
                                    <span asp-validation-for="UserName" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Email" class="form-label"></label>
                                    <input asp-for="Email" class="form-control" placeholder="Nhập email" />
                                    <span asp-validation-for="Email" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Phone" class="form-label"></label>
                                    <input asp-for="Phone" class="form-control" placeholder="Nhập số điện thoại" />
                                    <span asp-validation-for="Phone" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Name" class="form-label"></label>
                            <input asp-for="Name" class="form-control" placeholder="Nhập họ và tên đầy đủ" />
                            <span asp-validation-for="Name" class="text-danger"></span>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="DateOfBirth" class="form-label"></label>
                                    <input asp-for="DateOfBirth" class="form-control" type="date" />
                                    <span asp-validation-for="DateOfBirth" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Gender" class="form-label"></label>
                                    <select asp-for="Gender" class="form-select">
                                        <option value="">-- Chọn giới tính --</option>
                                        <option value="Nam">Nam</option>
                                        <option value="Nữ">Nữ</option>
                                        <option value="Khác">Khác</option>
                                    </select>
                                    <span asp-validation-for="Gender" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Address" class="form-label"></label>
                            <textarea asp-for="Address" class="form-control" rows="3" placeholder="Nhập địa chỉ đầy đủ"></textarea>
                            <span asp-validation-for="Address" class="text-danger"></span>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Password" class="form-label"></label>
                                    <input asp-for="Password" class="form-control" placeholder="Nhập mật khẩu" />
                                    <span asp-validation-for="Password" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="ConfirmPassword" class="form-label"></label>
                                    <input asp-for="ConfirmPassword" class="form-control" placeholder="Nhập lại mật khẩu" />
                                    <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Lưu ý:</h6>
                            <ul class="mb-0">
                                <li>Mật khẩu phải có ít nhất 6 ký tự</li>
                                <li>Thông tin cá nhân sẽ được bảo mật theo quy định</li>
                                <li>Sau khi đăng ký thành công, bạn có thể đặt lịch hẹn với bác sĩ</li>
                            </ul>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-action="Login" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại đăng nhập
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-user-plus"></i> Đăng ký
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Set max date for date of birth to today
        document.getElementById('DateOfBirth').max = new Date().toISOString().split('T')[0];
        
        // Auto-generate username from name
        document.getElementById('Name').addEventListener('input', function() {
            const name = this.value;
            if (name && !document.getElementById('UserName').value) {
                // Simple username generation (remove spaces and special chars)
                const username = name.toLowerCase()
                    .replace(/[^a-z0-9]/g, '')
                    .substring(0, 20);
                document.getElementById('UserName').value = username;
            }
        });
    </script>
}
