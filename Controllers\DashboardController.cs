using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Midterm1212.Models;
using Midterm1212.Models.ViewModels;

namespace Midterm1212.Controllers
{
    [Authorize]
    public class DashboardController : Controller
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly MedicalDbContext _context;

        public DashboardController(UserManager<ApplicationUser> userManager, MedicalDbContext context)
        {
            _userManager = userManager;
            _context = context;
        }

        public async Task<IActionResult> Index()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return RedirectToAction("Login", "Account");
            }

            return user.UserType switch
            {
                UserType.Patient => await PatientDashboard(user),
                UserType.Doctor => await DoctorDashboard(user),
                UserType.Admin => await AdminDashboard(),
                _ => RedirectToAction("Login", "Account")
            };
        }

        private async Task<IActionResult> PatientDashboard(ApplicationUser user)
        {
            var patient = await _context.Patients.FindAsync(user.PatientId);
            if (patient == null)
            {
                return RedirectToAction("Login", "Account");
            }

            var dashboardData = new
            {
                Patient = patient,
                UpcomingAppointments = await _context.Appointments
                    .Include(a => a.Doctor)
                    .Where(a => a.patient_id == user.PatientId && 
                               a.appointment_date >= DateTime.Today &&
                               a.status != "Cancelled")
                    .OrderBy(a => a.appointment_date)
                    .ThenBy(a => a.appointment_time)
                    .Take(5)
                    .ToListAsync(),

                RecentMedicalRecords = await _context.MedicalRecords
                    .Include(mr => mr.Doctor)
                    .Include(mr => mr.Prescriptions)
                    .Where(mr => mr.patient_id == user.PatientId)
                    .OrderByDescending(mr => mr.record_date)
                    .Take(3)
                    .ToListAsync(),

                PendingPrescriptions = await _context.Prescriptions
                    .Include(p => p.MedicalRecord)
                    .Where(p => p.MedicalRecord.patient_id == user.PatientId && 
                               p.Status == "Đã xử lý" && 
                               !p.MedicationOrders.Any())
                    .CountAsync(),

                TotalAppointments = await _context.Appointments
                    .CountAsync(a => a.patient_id == user.PatientId),

                TotalMedicalRecords = await _context.MedicalRecords
                    .CountAsync(mr => mr.patient_id == user.PatientId)
            };

            ViewBag.DashboardData = dashboardData;
            return View("Patient");
        }

        private async Task<IActionResult> DoctorDashboard(ApplicationUser user)
        {
            var doctor = await _context.Doctors.FindAsync(user.DoctorId);
            if (doctor == null)
            {
                return RedirectToAction("Login", "Account");
            }

            var today = DateTime.Today;
            var dashboardData = new
            {
                Doctor = doctor,
                TodayAppointments = await _context.Appointments
                    .Include(a => a.Patient)
                    .Where(a => a.doctor_id == user.DoctorId && 
                               a.appointment_date.Date == today &&
                               a.status != "Cancelled")
                    .OrderBy(a => a.appointment_time)
                    .ToListAsync(),

                PendingAppointments = await _context.Appointments
                    .Include(a => a.Patient)
                    .Where(a => a.doctor_id == user.DoctorId && 
                               a.status == "Pending")
                    .OrderBy(a => a.appointment_date)
                    .ThenBy(a => a.appointment_time)
                    .Take(5)
                    .ToListAsync(),

                RecentMedicalRecords = await _context.MedicalRecords
                    .Include(mr => mr.Patient)
                    .Where(mr => mr.doctor_id == user.DoctorId)
                    .OrderByDescending(mr => mr.record_date)
                    .Take(5)
                    .ToListAsync(),

                TotalPatients = await _context.Appointments
                    .Where(a => a.doctor_id == user.DoctorId)
                    .Select(a => a.patient_id)
                    .Distinct()
                    .CountAsync(),

                TotalAppointments = await _context.Appointments
                    .CountAsync(a => a.doctor_id == user.DoctorId),

                TotalPrescriptions = await _context.Prescriptions
                    .Include(p => p.MedicalRecord)
                    .CountAsync(p => p.MedicalRecord.doctor_id == user.DoctorId)
            };

            ViewBag.DashboardData = dashboardData;
            return View("Doctor");
        }

        private async Task<IActionResult> AdminDashboard()
        {
            var dashboardData = new AdminDashboardViewModel
            {
                TotalPatients = await _context.Patients.CountAsync(),
                TotalDoctors = await _context.Doctors.CountAsync(),
                TotalAppointments = await _context.Appointments.CountAsync(),
                TotalMedicines = await _context.Medicines.CountAsync(),
                TotalPrescriptions = await _context.Prescriptions.CountAsync(),
                TotalMedicationOrders = await _context.MedicationOrders.CountAsync(),

                LowStockMedicines = await _context.Medicines
                    .Where(m => m.QuantityInStock <= m.LowStockThreshold)
                    .CountAsync(),

                ExpiredMedicines = await _context.Medicines
                    .Where(m => m.ExpirationDate <= DateTime.Now)
                    .CountAsync(),

                PendingAppointments = await _context.Appointments
                    .CountAsync(a => a.status == "Pending"),

                PendingOrders = await _context.MedicationOrders
                    .CountAsync(o => o.OrderStatus == "Chờ xác nhận"),

                RecentAppointments = await _context.Appointments
                    .Include(a => a.Patient)
                    .Include(a => a.Doctor)
                    .OrderByDescending(a => a.appointment_date)
                    .ThenByDescending(a => a.appointment_time)
                    .Take(5)
                    .ToListAsync(),

                RecentOrders = await _context.MedicationOrders
                    .Include(o => o.Patient)
                    .Include(o => o.Prescription)
                    .OrderByDescending(o => o.CreatedDate)
                    .Take(5)
                    .ToListAsync(),

                MonthlyRevenue = await _context.MedicationOrders
                    .Where(o => o.OrderDate.Month == DateTime.Now.Month && 
                               o.OrderDate.Year == DateTime.Now.Year &&
                               o.OrderStatus == "Đã giao hàng")
                    .SumAsync(o => o.TotalAmount),

                TotalUsers = await _context.Users.CountAsync(),
                ActiveUsers = await _context.Users.CountAsync(u => u.IsActive)
            };

            return View("Admin", dashboardData);
        }
    }
}
