using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Midterm1212.Models
{
    public class MedicationOrder
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        [Required]
        public int Id { get; set; }

        [Required]
        [Display(Name = "<PERSON>ã bệnh nhân")]
        public int PatientId { get; set; }

        [Required]
        [Display(Name = "Mã đơn thuốc")]
        public int PrescriptionId { get; set; }

        [Required]
        [Display(Name = "Ngày đặt hàng")]
        [DataType(DataType.Date)]
        public DateTime OrderDate { get; set; }

        [Required]
        [Display(Name = "Tổng tiền")]
        [Column(TypeName = "decimal(12,2)")]
        public decimal TotalAmount { get; set; }

        [Required]
        [Display(Name = "Phương thức thanh toán")]
        public string PaymentMethod { get; set; } = "COD";

        [Required]
        [Display(Name = "Trạng thái đơn hàng")]
        public string OrderStatus { get; set; } = "Chờ xác nhận";

        [Required]
        [Display(Name = "Địa chỉ giao hàng")]
        public string DeliveryAddress { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Số điện thoại liên hệ")]
        public string ContactPhone { get; set; } = string.Empty;

        [Display(Name = "Ngày giao hàng dự kiến")]
        [DataType(DataType.Date)]
        public DateTime? EstimatedDeliveryDate { get; set; }

        [Display(Name = "Ngày giao hàng thực tế")]
        [DataType(DataType.Date)]
        public DateTime? ActualDeliveryDate { get; set; }

        [Display(Name = "Ghi chú")]
        public string? Notes { get; set; }

        [Display(Name = "Ngày tạo")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "Người tạo")]
        public string? CreatedBy { get; set; }

        [ForeignKey("PatientId")]
        public virtual Patient Patient { get; set; }

        [ForeignKey("PrescriptionId")]
        public virtual Prescription Prescription { get; set; }
    }
}
