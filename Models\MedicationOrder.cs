using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Midterm1212.Models
{
    public class MedicationOrder
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        [Required]
        public int order_id { get; set; }

        [Required]
        [Display(Name = "Đơn thuốc")]
        public int prescription_id { get; set; }

        [Required]
        [Display(Name = "Bệnh nhân")]
        public int patient_id { get; set; }

        [Required]
        [Display(Name = "Ngày đặt hàng")]
        [DataType(DataType.Date)]
        public DateTime order_date { get; set; } = DateTime.Now;

        [Required]
        [Display(Name = "Trạng thái đơn hàng")]
        public string order_status { get; set; } = "Chờ xử lý"; // Ch<PERSON> xử lý, <PERSON>ang chuẩn bị, <PERSON><PERSON> giao, <PERSON><PERSON> giao, <PERSON><PERSON>y

        [Required]
        [Display(Name = "<PERSON><PERSON><PERSON><PERSON> thức thanh toán")]
        public string payment_method { get; set; } = "COD"; // COD, <PERSON><PERSON><PERSON><PERSON> khoản, Tiề<PERSON> mặt

        [Required]
        [Display(Name = "Trạng thái thanh toán")]
        public string payment_status { get; set; } = "Chưa thanh toán"; // Chưa thanh toán, Đã thanh toán, Hoàn tiền

        [Required]
        [Column(TypeName = "decimal(12,2)")]
        [Display(Name = "Tổng tiền")]
        public decimal total_amount { get; set; }

        [Column(TypeName = "decimal(12,2)")]
        [Display(Name = "Phí giao hàng")]
        public decimal shipping_fee { get; set; } = 0;

        [Column(TypeName = "decimal(12,2)")]
        [Display(Name = "Tổng thanh toán")]
        public decimal final_amount { get; set; }

        [Required]
        [Display(Name = "Địa chỉ giao hàng")]
        public string delivery_address { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Số điện thoại liên hệ")]
        public string contact_phone { get; set; } = string.Empty;

        [Display(Name = "Ghi chú")]
        public string? notes { get; set; }

        [Display(Name = "Ngày giao hàng dự kiến")]
        [DataType(DataType.Date)]
        public DateTime? expected_delivery_date { get; set; }

        [Display(Name = "Ngày giao hàng thực tế")]
        [DataType(DataType.Date)]
        public DateTime? actual_delivery_date { get; set; }

        [Required]
        [Display(Name = "Ngày tạo")]
        public DateTime created_date { get; set; } = DateTime.Now;

        [Display(Name = "Ngày cập nhật")]
        public DateTime? updated_date { get; set; }

        [Display(Name = "Người cập nhật")]
        public string? updated_by { get; set; }

        [ForeignKey("prescription_id")]
        public virtual Prescription Prescription { get; set; }

        [ForeignKey("patient_id")]
        public virtual Patient Patient { get; set; }
    }
}
