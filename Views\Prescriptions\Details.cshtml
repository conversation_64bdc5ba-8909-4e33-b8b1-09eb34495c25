@model Midterm1212.Models.Prescription

@{
    ViewData["Title"] = "Chi tiết đơn thuốc";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1>Chi tiết đơn thuốc #@Model.prescription_id</h1>

            <!-- Prescription Information -->
            <div class="card mb-3">
                <div class="card-header">
                    <h4>Thông tin đơn thuốc</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Mã đơn thuốc:</dt>
                                <dd class="col-sm-8">@Html.DisplayFor(model => model.prescription_id)</dd>

                                <dt class="col-sm-4">Bệnh nhân:</dt>
                                <dd class="col-sm-8">@Html.DisplayFor(model => model.Patient.name)</dd>

                                <dt class="col-sm-4"><PERSON><PERSON><PERSON> sĩ kê đơn:</dt>
                                <dd class="col-sm-8">@Html.DisplayFor(model => model.Doctor.name)</dd>

                                <dt class="col-sm-4">Chuyên khoa:</dt>
                                <dd class="col-sm-8">@Html.DisplayFor(model => model.Doctor.specialization)</dd>

                                @if (Model.MedicalRecord != null)
                                {
                                    <dt class="col-sm-4">Hồ sơ y tế:</dt>
                                    <dd class="col-sm-8">@Html.DisplayFor(model => model.MedicalRecord.diagnosis)</dd>
                                }
                            </dl>
                        </div>

                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Ngày kê đơn:</dt>
                                <dd class="col-sm-8">@Model.prescription_date.ToString("dd/MM/yyyy")</dd>

                                <dt class="col-sm-4">Trạng thái:</dt>
                                <dd class="col-sm-8">
                                    @switch (Model.status)
                                    {
                                        case "Chờ xử lý":
                                            <span class="badge badge-warning">@Model.status</span>
                                            break;
                                        case "Đã cấp thuốc":
                                            <span class="badge badge-info">@Model.status</span>
                                            break;
                                        case "Hoàn thành":
                                            <span class="badge badge-success">@Model.status</span>
                                            break;
                                        case "Hủy":
                                            <span class="badge badge-danger">@Model.status</span>
                                            break;
                                        default:
                                            <span class="badge badge-secondary">@Model.status</span>
                                            break;
                                    }
                                </dd>

                                <dt class="col-sm-4">Tổng tiền:</dt>
                                <dd class="col-sm-8"><strong>@Model.total_amount.ToString("N0") VNĐ</strong></dd>

                                <dt class="col-sm-4">Ngày tạo:</dt>
                                <dd class="col-sm-8">@Model.created_date.ToString("dd/MM/yyyy HH:mm")</dd>

                                @if (Model.updated_date.HasValue)
                                {
                                    <dt class="col-sm-4">Ngày cập nhật:</dt>
                                    <dd class="col-sm-8">@Model.updated_date.Value.ToString("dd/MM/yyyy HH:mm")</dd>
                                }
                            </dl>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.notes))
                    {
                        <div class="row">
                            <div class="col-md-12">
                                <dt>Ghi chú:</dt>
                                <dd>@Html.DisplayFor(model => model.notes)</dd>
                            </div>
                        </div>
                    }
                </div>
            </div>

            <!-- Prescription Items -->
            <div class="card mb-3">
                <div class="card-header">
                    <h4>Danh sách thuốc</h4>
                </div>
                <div class="card-body">
                    @if (Model.PrescriptionItems.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Tên thuốc</th>
                                        <th>Hoạt chất</th>
                                        <th>Danh mục</th>
                                        <th>Số lượng</th>
                                        <th>Liều dùng</th>
                                        <th>Tần suất</th>
                                        <th>Thời gian</th>
                                        <th>Đơn giá</th>
                                        <th>Thành tiền</th>
                                        <th>Ghi chú</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in Model.PrescriptionItems)
                                    {
                                        <tr>
                                            <td><strong>@item.Medication.medication_name</strong></td>
                                            <td>@item.Medication.active_ingredient</td>
                                            <td>@item.Medication.Category?.category_name</td>
                                            <td>@item.quantity</td>
                                            <td>@item.dosage_instructions</td>
                                            <td>@item.frequency</td>
                                            <td>@item.duration</td>
                                            <td>@item.unit_price.ToString("N0") VNĐ</td>
                                            <td><strong>@item.total_price.ToString("N0") VNĐ</strong></td>
                                            <td>@item.notes</td>
                                        </tr>
                                    }
                                </tbody>
                                <tfoot>
                                    <tr class="table-info">
                                        <th colspan="8">Tổng cộng:</th>
                                        <th><strong>@Model.total_amount.ToString("N0") VNĐ</strong></th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info text-center">
                            <h5>Chưa có thuốc nào trong đơn</h5>
                            <p>Đơn thuốc này chưa có thuốc nào được thêm vào.</p>
                            @if (Model.status == "Chờ xử lý")
                            {
                                <a asp-action="AddItems" asp-route-id="@Model.prescription_id" class="btn btn-success">Thêm thuốc</a>
                            }
                        </div>
                    }
                </div>
            </div>

            <!-- Medication Orders -->
            @if (Model.MedicationOrders.Any())
            {
                <div class="card mb-3">
                    <div class="card-header">
                        <h4>Đơn hàng giao thuốc</h4>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Mã đơn hàng</th>
                                        <th>Ngày đặt</th>
                                        <th>Trạng thái</th>
                                        <th>Thanh toán</th>
                                        <th>Tổng tiền</th>
                                        <th>Địa chỉ giao</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var order in Model.MedicationOrders)
                                    {
                                        <tr>
                                            <td>@order.order_id</td>
                                            <td>@order.order_date.ToString("dd/MM/yyyy")</td>
                                            <td>
                                                <span class="badge @(order.order_status == "Đã giao" ? "badge-success" : 
                                                                   order.order_status == "Đang giao" ? "badge-info" : 
                                                                   order.order_status == "Hủy" ? "badge-danger" : "badge-warning")">
                                                    @order.order_status
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge @(order.payment_status == "Đã thanh toán" ? "badge-success" : "badge-warning")">
                                                    @order.payment_status
                                                </span>
                                            </td>
                                            <td>@order.final_amount.ToString("N0") VNĐ</td>
                                            <td>@order.delivery_address</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }

            <div class="mt-3">
                @if (Model.status == "Chờ xử lý")
                {
                    <a asp-action="Edit" asp-route-id="@Model.prescription_id" class="btn btn-primary">Chỉnh sửa</a>
                    <a asp-action="AddItems" asp-route-id="@Model.prescription_id" class="btn btn-success">Thêm thuốc</a>
                }
                <a asp-action="Index" class="btn btn-secondary">Quay lại danh sách</a>
                <a asp-action="Delete" asp-route-id="@Model.prescription_id" class="btn btn-danger">Xóa</a>
                
                @if (Model.PrescriptionItems.Any())
                {
                    <button onclick="window.print()" class="btn btn-info">In đơn thuốc</button>
                }
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-md-12 text-left">
            © 2025 - Student's ID - Your Name
        </div>
    </div>
</div>

<style>
    @media print {
        .btn, .navbar, .footer {
            display: none !important;
        }
        .card {
            border: 1px solid #000 !important;
            box-shadow: none !important;
        }
    }
</style>
