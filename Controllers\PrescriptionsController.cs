using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Midterm1212.Models;

namespace Midterm1212.Controllers
{
    public class PrescriptionsController : Controller
    {
        private readonly MedicalDbContext _context;

        public PrescriptionsController(MedicalDbContext context)
        {
            _context = context;
        }

        // GET: Prescriptions
        public async Task<IActionResult> Index(string searchString, string status, int? patientId, int? doctorId)
        {
            var prescriptions = _context.Prescriptions
                .Include(p => p.Patient)
                .Include(p => p.Doctor)
                .Include(p => p.MedicalRecord)
                .Include(p => p.PrescriptionItems)
                .ThenInclude(pi => pi.Medication)
                .AsQueryable();

            if (!string.IsNullOrEmpty(searchString))
            {
                prescriptions = prescriptions.Where(p => p.Patient.name.Contains(searchString) ||
                                                       p.Doctor.name.Contains(searchString));
            }

            if (!string.IsNullOrEmpty(status))
            {
                prescriptions = prescriptions.Where(p => p.status == status);
            }

            if (patientId.HasValue)
            {
                prescriptions = prescriptions.Where(p => p.patient_id == patientId.Value);
            }

            if (doctorId.HasValue)
            {
                prescriptions = prescriptions.Where(p => p.doctor_id == doctorId.Value);
            }

            ViewData["Patients"] = new SelectList(await _context.Patients.ToListAsync(), "patient_id", "name");
            ViewData["Doctors"] = new SelectList(await _context.Doctors.ToListAsync(), "doctor_id", "name");
            ViewData["StatusList"] = new SelectList(new[] { "Chờ xử lý", "Đã cấp thuốc", "Hoàn thành", "Hủy" });
            ViewData["CurrentFilter"] = searchString;
            ViewData["CurrentStatus"] = status;
            ViewData["CurrentPatient"] = patientId;
            ViewData["CurrentDoctor"] = doctorId;

            return View(await prescriptions.OrderByDescending(p => p.prescription_date).ToListAsync());
        }

        // GET: Prescriptions/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var prescription = await _context.Prescriptions
                .Include(p => p.Patient)
                .Include(p => p.Doctor)
                .Include(p => p.MedicalRecord)
                .Include(p => p.PrescriptionItems)
                .ThenInclude(pi => pi.Medication)
                .ThenInclude(m => m.Category)
                .Include(p => p.MedicationOrders)
                .FirstOrDefaultAsync(m => m.prescription_id == id);

            if (prescription == null)
            {
                return NotFound();
            }

            return View(prescription);
        }

        // GET: Prescriptions/Create
        public IActionResult Create()
        {
            ViewData["patient_id"] = new SelectList(_context.Patients, "patient_id", "name");
            ViewData["doctor_id"] = new SelectList(_context.Doctors, "doctor_id", "name");
            ViewData["medical_record_id"] = new SelectList(_context.MedicalRecords, "record_id", "diagnosis");
            ViewData["medications"] = new SelectList(_context.Medications.Where(m => m.stock_quantity > 0), "medication_id", "medication_name");
            return View();
        }

        // POST: Prescriptions/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("prescription_id,patient_id,doctor_id,medical_record_id,prescription_date,status,notes")] Prescription prescription)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    foreach (var error in ModelState)
                    {
                        foreach (var subError in error.Value.Errors)
                        {
                            ModelState.AddModelError("", $"{error.Key}: {subError.ErrorMessage}");
                        }
                    }
                    ViewData["patient_id"] = new SelectList(_context.Patients, "patient_id", "name", prescription.patient_id);
                    ViewData["doctor_id"] = new SelectList(_context.Doctors, "doctor_id", "name", prescription.doctor_id);
                    ViewData["medical_record_id"] = new SelectList(_context.MedicalRecords, "record_id", "diagnosis", prescription.medical_record_id);
                    ViewData["medications"] = new SelectList(_context.Medications.Where(m => m.stock_quantity > 0), "medication_id", "medication_name");
                    return View(prescription);
                }

                prescription.created_date = DateTime.Now;
                prescription.total_amount = 0; // Will be calculated when items are added
                _context.Add(prescription);
                await _context.SaveChangesAsync();
                return RedirectToAction("AddItems", new { id = prescription.prescription_id });
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "Đã xảy ra lỗi khi lưu: " + ex.Message);
                ViewData["patient_id"] = new SelectList(_context.Patients, "patient_id", "name", prescription.patient_id);
                ViewData["doctor_id"] = new SelectList(_context.Doctors, "doctor_id", "name", prescription.doctor_id);
                ViewData["medical_record_id"] = new SelectList(_context.MedicalRecords, "record_id", "diagnosis", prescription.medical_record_id);
                ViewData["medications"] = new SelectList(_context.Medications.Where(m => m.stock_quantity > 0), "medication_id", "medication_name");
                return View(prescription);
            }
        }

        // GET: Prescriptions/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var prescription = await _context.Prescriptions.FindAsync(id);
            if (prescription == null)
            {
                return NotFound();
            }
            ViewData["patient_id"] = new SelectList(_context.Patients, "patient_id", "name", prescription.patient_id);
            ViewData["doctor_id"] = new SelectList(_context.Doctors, "doctor_id", "name", prescription.doctor_id);
            ViewData["medical_record_id"] = new SelectList(_context.MedicalRecords, "record_id", "diagnosis", prescription.medical_record_id);
            return View(prescription);
        }

        // POST: Prescriptions/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("prescription_id,patient_id,doctor_id,medical_record_id,prescription_date,status,notes,total_amount,created_date")] Prescription prescription)
        {
            if (id != prescription.prescription_id)
            {
                return NotFound();
            }

            try
            {
                if (!ModelState.IsValid)
                {
                    foreach (var error in ModelState)
                    {
                        foreach (var subError in error.Value.Errors)
                        {
                            ModelState.AddModelError("", $"{error.Key}: {subError.ErrorMessage}");
                        }
                    }
                    ViewData["patient_id"] = new SelectList(_context.Patients, "patient_id", "name", prescription.patient_id);
                    ViewData["doctor_id"] = new SelectList(_context.Doctors, "doctor_id", "name", prescription.doctor_id);
                    ViewData["medical_record_id"] = new SelectList(_context.MedicalRecords, "record_id", "diagnosis", prescription.medical_record_id);
                    return View(prescription);
                }

                prescription.updated_date = DateTime.Now;
                _context.Update(prescription);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!PrescriptionExists(prescription.prescription_id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "Đã xảy ra lỗi khi cập nhật: " + ex.Message);
                ViewData["patient_id"] = new SelectList(_context.Patients, "patient_id", "name", prescription.patient_id);
                ViewData["doctor_id"] = new SelectList(_context.Doctors, "doctor_id", "name", prescription.doctor_id);
                ViewData["medical_record_id"] = new SelectList(_context.MedicalRecords, "record_id", "diagnosis", prescription.medical_record_id);
                return View(prescription);
            }
        }

        // GET: Prescriptions/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var prescription = await _context.Prescriptions
                .Include(p => p.Patient)
                .Include(p => p.Doctor)
                .Include(p => p.MedicalRecord)
                .Include(p => p.PrescriptionItems)
                .ThenInclude(pi => pi.Medication)
                .FirstOrDefaultAsync(m => m.prescription_id == id);
            if (prescription == null)
            {
                return NotFound();
            }

            return View(prescription);
        }

        // POST: Prescriptions/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var prescription = await _context.Prescriptions.FindAsync(id);
            if (prescription != null)
            {
                _context.Prescriptions.Remove(prescription);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        // GET: Prescriptions/AddItems/5
        public async Task<IActionResult> AddItems(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var prescription = await _context.Prescriptions
                .Include(p => p.Patient)
                .Include(p => p.Doctor)
                .Include(p => p.PrescriptionItems)
                .ThenInclude(pi => pi.Medication)
                .FirstOrDefaultAsync(p => p.prescription_id == id);

            if (prescription == null)
            {
                return NotFound();
            }

            ViewData["medications"] = new SelectList(_context.Medications.Where(m => m.stock_quantity > 0), "medication_id", "medication_name");
            return View(prescription);
        }

        // POST: Prescriptions/AddItem
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddItem(int prescriptionId, int medicationId, int quantity, string dosageInstructions, string frequency, string duration, string notes)
        {
            try
            {
                var medication = await _context.Medications.FindAsync(medicationId);
                if (medication == null || medication.stock_quantity < quantity)
                {
                    return Json(new { success = false, message = "Thuốc không tồn tại hoặc không đủ số lượng trong kho" });
                }

                var prescriptionItem = new PrescriptionItem
                {
                    prescription_item_id = await GetNextPrescriptionItemId(),
                    prescription_id = prescriptionId,
                    medication_id = medicationId,
                    quantity = quantity,
                    dosage_instructions = dosageInstructions,
                    frequency = frequency,
                    duration = duration,
                    unit_price = medication.unit_price,
                    total_price = medication.unit_price * quantity,
                    notes = notes
                };

                _context.PrescriptionItems.Add(prescriptionItem);
                await _context.SaveChangesAsync();

                // Update prescription total
                await UpdatePrescriptionTotal(prescriptionId);

                return Json(new { success = true, message = "Đã thêm thuốc vào đơn thành công" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Đã xảy ra lỗi: " + ex.Message });
            }
        }

        private bool PrescriptionExists(int id)
        {
            return _context.Prescriptions.Any(e => e.prescription_id == id);
        }

        private async Task<int> GetNextPrescriptionItemId()
        {
            var lastItem = await _context.PrescriptionItems.OrderByDescending(pi => pi.prescription_item_id).FirstOrDefaultAsync();
            return lastItem?.prescription_item_id + 1 ?? 1;
        }

        private async Task UpdatePrescriptionTotal(int prescriptionId)
        {
            var prescription = await _context.Prescriptions
                .Include(p => p.PrescriptionItems)
                .FirstOrDefaultAsync(p => p.prescription_id == prescriptionId);

            if (prescription != null)
            {
                prescription.total_amount = prescription.PrescriptionItems.Sum(pi => pi.total_price);
                prescription.updated_date = DateTime.Now;
                await _context.SaveChangesAsync();
            }
        }
    }
}
