using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Midterm1212.Models;

namespace Midterm1212.Controllers
{
    [Authorize(Roles = "Admin,Doctor")]
    public class PrescriptionsController : Controller
    {
        private readonly MedicalDbContext _context;

        public PrescriptionsController(MedicalDbContext context)
        {
            _context = context;
        }

        public async Task<IActionResult> Index(string searchString, string statusFilter, string sortOrder)
        {
            ViewData["CurrentFilter"] = searchString;
            ViewData["StatusFilter"] = statusFilter;
            ViewData["DateSortParm"] = String.IsNullOrEmpty(sortOrder) ? "date_desc" : "";
            ViewData["StatusSortParm"] = sortOrder == "Status" ? "status_desc" : "Status";
            ViewData["TotalSortParm"] = sortOrder == "Total" ? "total_desc" : "Total";

            var prescriptions = from p in _context.Prescriptions
                               .Include(p => p.MedicalRecord)
                               .ThenInclude(mr => mr.Patient)
                               .Include(p => p.PrescriptionDetails)
                               .ThenInclude(pd => pd.Medicine)
                               select p;

            if (!String.IsNullOrEmpty(searchString))
            {
                prescriptions = prescriptions.Where(p => p.MedicalRecord.Patient.name.Contains(searchString)
                                                      || p.Instructions.Contains(searchString)
                                                      || p.Notes.Contains(searchString));
            }

            if (!String.IsNullOrEmpty(statusFilter))
            {
                prescriptions = prescriptions.Where(p => p.Status == statusFilter);
            }

            switch (sortOrder)
            {
                case "date_desc":
                    prescriptions = prescriptions.OrderByDescending(p => p.PrescriptionDate);
                    break;
                case "Status":
                    prescriptions = prescriptions.OrderBy(p => p.Status);
                    break;
                case "status_desc":
                    prescriptions = prescriptions.OrderByDescending(p => p.Status);
                    break;
                case "Total":
                    prescriptions = prescriptions.OrderBy(p => p.TotalCost);
                    break;
                case "total_desc":
                    prescriptions = prescriptions.OrderByDescending(p => p.TotalCost);
                    break;
                default:
                    prescriptions = prescriptions.OrderBy(p => p.PrescriptionDate);
                    break;
            }

            ViewBag.StatusList = new SelectList(new[]
            {
                new { Value = "", Text = "Tất cả trạng thái" },
                new { Value = "Chờ xử lý", Text = "Chờ xử lý" },
                new { Value = "Đã xử lý", Text = "Đã xử lý" },
                new { Value = "Hoàn thành", Text = "Hoàn thành" },
                new { Value = "Đã hủy", Text = "Đã hủy" }
            }, "Value", "Text", statusFilter);

            return View(await prescriptions.AsNoTracking().ToListAsync());
        }

        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var prescription = await _context.Prescriptions
                .Include(p => p.MedicalRecord)
                .ThenInclude(mr => mr.Patient)
                .Include(p => p.MedicalRecord.Doctor)
                .Include(p => p.PrescriptionDetails)
                .ThenInclude(pd => pd.Medicine)
                .Include(p => p.MedicationOrders)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (prescription == null)
            {
                return NotFound();
            }

            return View(prescription);
        }

        public async Task<IActionResult> Create()
        {
            ViewData["MedicalRecordId"] = new SelectList(
                await _context.MedicalRecords
                    .Include(mr => mr.Patient)
                    .Select(mr => new { 
                        mr.record_id, 
                        DisplayText = $"#{mr.record_id} - {mr.Patient.name} - {mr.diagnosis}" 
                    })
                    .ToListAsync(), 
                "record_id", "DisplayText");

            ViewData["Medicines"] = await _context.Medicines
                .Where(m => m.QuantityInStock > 0)
                .Select(m => new { 
                    m.Id, 
                    m.Name, 
                    m.Price, 
                    m.QuantityInStock,
                    DisplayText = $"{m.Name} - {m.Price:C} (Tồn: {m.QuantityInStock})"
                })
                .ToListAsync();

            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,MedicalRecordId,PrescriptionDate,Instructions,IsPaid,Status,Notes")] Prescription prescription, List<PrescriptionDetailViewModel> details)
        {
            if (ModelState.IsValid)
            {
                var existingPrescription = await _context.Prescriptions.FindAsync(prescription.Id);
                if (existingPrescription != null)
                {
                    ModelState.AddModelError("Id", "Mã đơn thuốc đã tồn tại.");
                    await LoadCreateViewData();
                    return View(prescription);
                }

                prescription.CreatedDate = DateTime.Now;
                prescription.TotalCost = 0;

                _context.Add(prescription);

                if (details != null && details.Any())
                {
                    foreach (var detail in details.Where(d => d.MedicineId > 0 && d.Quantity > 0))
                    {
                        var medicine = await _context.Medicines.FindAsync(detail.MedicineId);
                        if (medicine != null && medicine.QuantityInStock >= detail.Quantity)
                        {
                            var prescriptionDetail = new PrescriptionDetail
                            {
                                Id = detail.Id,
                                PrescriptionId = prescription.Id,
                                MedicineId = detail.MedicineId,
                                Quantity = detail.Quantity,
                                Dosage = detail.Dosage,
                                Frequency = detail.Frequency,
                                Duration = detail.Duration,
                                Instructions = detail.Instructions,
                                UnitPrice = medicine.Price,
                                TotalPrice = medicine.Price * detail.Quantity,
                                Notes = detail.Notes
                            };

                            _context.PrescriptionDetails.Add(prescriptionDetail);
                            prescription.TotalCost += prescriptionDetail.TotalPrice;

                            medicine.QuantityInStock -= detail.Quantity;
                            _context.Update(medicine);
                        }
                    }
                }

                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "Đơn thuốc đã được tạo thành công.";
                return RedirectToAction(nameof(Index));
            }

            await LoadCreateViewData();
            return View(prescription);
        }

        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var prescription = await _context.Prescriptions
                .Include(p => p.PrescriptionDetails)
                .ThenInclude(pd => pd.Medicine)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (prescription == null)
            {
                return NotFound();
            }

            ViewData["MedicalRecordId"] = new SelectList(
                await _context.MedicalRecords
                    .Include(mr => mr.Patient)
                    .Select(mr => new { 
                        mr.record_id, 
                        DisplayText = $"#{mr.record_id} - {mr.Patient.name} - {mr.diagnosis}" 
                    })
                    .ToListAsync(), 
                "record_id", "DisplayText", prescription.MedicalRecordId);

            return View(prescription);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,MedicalRecordId,PrescriptionDate,Instructions,IsPaid,TotalCost,Status,CreatedDate,Notes")] Prescription prescription)
        {
            if (id != prescription.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(prescription);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "Đơn thuốc đã được cập nhật thành công.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!PrescriptionExists(prescription.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }

            ViewData["MedicalRecordId"] = new SelectList(
                await _context.MedicalRecords
                    .Include(mr => mr.Patient)
                    .Select(mr => new { 
                        mr.record_id, 
                        DisplayText = $"#{mr.record_id} - {mr.Patient.name} - {mr.diagnosis}" 
                    })
                    .ToListAsync(), 
                "record_id", "DisplayText", prescription.MedicalRecordId);

            return View(prescription);
        }

        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var prescription = await _context.Prescriptions
                .Include(p => p.MedicalRecord)
                .ThenInclude(mr => mr.Patient)
                .Include(p => p.PrescriptionDetails)
                .ThenInclude(pd => pd.Medicine)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (prescription == null)
            {
                return NotFound();
            }

            return View(prescription);
        }

        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var prescription = await _context.Prescriptions
                .Include(p => p.PrescriptionDetails)
                .Include(p => p.MedicationOrders)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (prescription != null)
            {
                if (prescription.MedicationOrders.Any())
                {
                    TempData["ErrorMessage"] = "Không thể xóa đơn thuốc này vì đã có đơn hàng liên quan.";
                    return RedirectToAction(nameof(Index));
                }

                foreach (var detail in prescription.PrescriptionDetails)
                {
                    var medicine = await _context.Medicines.FindAsync(detail.MedicineId);
                    if (medicine != null)
                    {
                        medicine.QuantityInStock += detail.Quantity;
                        _context.Update(medicine);
                    }
                }

                _context.Prescriptions.Remove(prescription);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "Đơn thuốc đã được xóa thành công.";
            }

            return RedirectToAction(nameof(Index));
        }

        private async Task LoadCreateViewData()
        {
            ViewData["MedicalRecordId"] = new SelectList(
                await _context.MedicalRecords
                    .Include(mr => mr.Patient)
                    .Select(mr => new { 
                        mr.record_id, 
                        DisplayText = $"#{mr.record_id} - {mr.Patient.name} - {mr.diagnosis}" 
                    })
                    .ToListAsync(), 
                "record_id", "DisplayText");

            ViewData["Medicines"] = await _context.Medicines
                .Where(m => m.QuantityInStock > 0)
                .Select(m => new { 
                    m.Id, 
                    m.Name, 
                    m.Price, 
                    m.QuantityInStock,
                    DisplayText = $"{m.Name} - {m.Price:C} (Tồn: {m.QuantityInStock})"
                })
                .ToListAsync();
        }

        // GET: Prescriptions/CreatePrescription/5 (from medical record)
        public async Task<IActionResult> CreatePrescription(int? medicalRecordId)
        {
            if (medicalRecordId == null)
            {
                return NotFound();
            }

            var medicalRecord = await _context.MedicalRecords
                .Include(mr => mr.Patient)
                .Include(mr => mr.Doctor)
                .Include(mr => mr.Appointment)
                .FirstOrDefaultAsync(mr => mr.record_id == medicalRecordId);

            if (medicalRecord == null)
            {
                return NotFound();
            }

            // Check if prescription already exists for this medical record
            var existingPrescription = await _context.Prescriptions
                .FirstOrDefaultAsync(p => p.MedicalRecordId == medicalRecordId);
            if (existingPrescription != null)
            {
                TempData["ErrorMessage"] = "Hồ sơ bệnh án này đã có đơn thuốc.";
                return RedirectToAction("ViewPrescriptionResult", new { prescriptionId = existingPrescription.Id });
            }

            var maxPrescriptionId = await _context.Prescriptions.AnyAsync()
                ? await _context.Prescriptions.MaxAsync(p => p.Id)
                : 0;

            var prescription = new Prescription
            {
                Id = maxPrescriptionId + 1,
                MedicalRecordId = medicalRecordId.Value,
                PrescriptionDate = DateTime.Now,
                Status = "Chờ xử lý",
                IsPaid = false,
                TotalCost = 0,
                CreatedDate = DateTime.Now
            };

            ViewData["Medicines"] = await _context.Medicines
                .Where(m => m.QuantityInStock > 0)
                .Select(m => new {
                    m.Id,
                    m.Name,
                    m.Price,
                    m.QuantityInStock,
                    m.Description,
                    DisplayText = $"{m.Name} - {m.Price:C} (Tồn: {m.QuantityInStock})"
                })
                .ToListAsync();

            ViewBag.MedicalRecord = medicalRecord;
            return View(prescription);
        }

        // POST: Prescriptions/CreatePrescription
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreatePrescription([Bind("Id,MedicalRecordId,PrescriptionDate,Instructions,IsPaid,Status,Notes")] Prescription prescription, List<PrescriptionDetailViewModel> details)
        {
            if (ModelState.IsValid)
            {
                // Check if prescription already exists for this medical record
                var existingPrescription = await _context.Prescriptions
                    .FirstOrDefaultAsync(p => p.MedicalRecordId == prescription.MedicalRecordId);
                if (existingPrescription != null)
                {
                    TempData["ErrorMessage"] = "Hồ sơ bệnh án này đã có đơn thuốc.";
                    return RedirectToAction("ViewPrescriptionResult", new { prescriptionId = existingPrescription.Id });
                }

                prescription.CreatedDate = DateTime.Now;
                prescription.TotalCost = 0;

                _context.Add(prescription);

                if (details != null && details.Any())
                {
                    var maxDetailId = await _context.PrescriptionDetails.AnyAsync()
                        ? await _context.PrescriptionDetails.MaxAsync(pd => pd.Id)
                        : 0;

                    foreach (var detail in details.Where(d => d.MedicineId > 0 && d.Quantity > 0))
                    {
                        var medicine = await _context.Medicines.FindAsync(detail.MedicineId);
                        if (medicine != null && medicine.QuantityInStock >= detail.Quantity)
                        {
                            var prescriptionDetail = new PrescriptionDetail
                            {
                                Id = ++maxDetailId,
                                PrescriptionId = prescription.Id,
                                MedicineId = detail.MedicineId,
                                Quantity = detail.Quantity,
                                Dosage = detail.Dosage,
                                Frequency = detail.Frequency,
                                Duration = detail.Duration,
                                Instructions = detail.Instructions,
                                UnitPrice = medicine.Price,
                                TotalPrice = medicine.Price * detail.Quantity,
                                Notes = detail.Notes
                            };

                            _context.PrescriptionDetails.Add(prescriptionDetail);
                            prescription.TotalCost += prescriptionDetail.TotalPrice;

                            // Reserve medicine stock
                            medicine.QuantityInStock -= detail.Quantity;
                            _context.Update(medicine);
                        }
                    }
                }

                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "Đơn thuốc đã được tạo thành công!";
                return RedirectToAction("ViewPrescriptionResult", new { prescriptionId = prescription.Id });
            }

            var medicalRecord = await _context.MedicalRecords
                .Include(mr => mr.Patient)
                .Include(mr => mr.Doctor)
                .Include(mr => mr.Appointment)
                .FirstOrDefaultAsync(mr => mr.record_id == prescription.MedicalRecordId);

            ViewData["Medicines"] = await _context.Medicines
                .Where(m => m.QuantityInStock > 0)
                .Select(m => new {
                    m.Id,
                    m.Name,
                    m.Price,
                    m.QuantityInStock,
                    m.Description,
                    DisplayText = $"{m.Name} - {m.Price:C} (Tồn: {m.QuantityInStock})"
                })
                .ToListAsync();

            ViewBag.MedicalRecord = medicalRecord;
            return View(prescription);
        }

        // GET: Prescriptions/ViewPrescriptionResult/5
        public async Task<IActionResult> ViewPrescriptionResult(int? prescriptionId)
        {
            if (prescriptionId == null)
            {
                return NotFound();
            }

            var prescription = await _context.Prescriptions
                .Include(p => p.MedicalRecord)
                .ThenInclude(mr => mr.Patient)
                .Include(p => p.MedicalRecord.Doctor)
                .Include(p => p.MedicalRecord.Appointment)
                .Include(p => p.PrescriptionDetails)
                .ThenInclude(pd => pd.Medicine)
                .FirstOrDefaultAsync(p => p.Id == prescriptionId);

            if (prescription == null)
            {
                return NotFound();
            }

            return View(prescription);
        }

        private bool PrescriptionExists(int id)
        {
            return _context.Prescriptions.Any(e => e.Id == id);
        }
    }

    public class PrescriptionDetailViewModel
    {
        public int Id { get; set; }
        public int MedicineId { get; set; }
        public int Quantity { get; set; }
        public string Dosage { get; set; } = string.Empty;
        public string? Frequency { get; set; }
        public string? Duration { get; set; }
        public string? Instructions { get; set; }
        public string? Notes { get; set; }
    }
}
