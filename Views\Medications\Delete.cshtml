@model Midterm1212.Models.Medication

@{
    ViewData["Title"] = "Xóa thuốc";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1><PERSON><PERSON><PERSON> thuốc</h1>

            <div class="alert alert-danger">
                <h4>Bạn có chắc chắn muốn xóa thuốc này?</h4>
                <p>Hành động này không thể hoàn tác!</p>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3>@Html.DisplayFor(model => model.medication_name)</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Mã thuốc:</dt>
                                <dd class="col-sm-8">@Html.DisplayFor(model => model.medication_id)</dd>

                                <dt class="col-sm-4">Tên thuốc:</dt>
                                <dd class="col-sm-8">@Html.DisplayFor(model => model.medication_name)</dd>

                                <dt class="col-sm-4">Thành phần hoạt chất:</dt>
                                <dd class="col-sm-8">@Html.DisplayFor(model => model.active_ingredient)</dd>

                                <dt class="col-sm-4">Liều lượng:</dt>
                                <dd class="col-sm-8">@Html.DisplayFor(model => model.dosage)</dd>

                                <dt class="col-sm-4">Dạng thuốc:</dt>
                                <dd class="col-sm-8">@Html.DisplayFor(model => model.form)</dd>

                                <dt class="col-sm-4">Nhà sản xuất:</dt>
                                <dd class="col-sm-8">@Html.DisplayFor(model => model.manufacturer)</dd>

                                <dt class="col-sm-4">Danh mục:</dt>
                                <dd class="col-sm-8">@Html.DisplayFor(model => model.Category.category_name)</dd>
                            </dl>
                        </div>

                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Số lượng tồn kho:</dt>
                                <dd class="col-sm-8">@Html.DisplayFor(model => model.stock_quantity)</dd>

                                <dt class="col-sm-4">Số lượng tối thiểu:</dt>
                                <dd class="col-sm-8">@Html.DisplayFor(model => model.minimum_stock)</dd>

                                <dt class="col-sm-4">Giá bán:</dt>
                                <dd class="col-sm-8">@Model.unit_price.ToString("N0") VNĐ</dd>

                                <dt class="col-sm-4">Ngày sản xuất:</dt>
                                <dd class="col-sm-8">@Model.manufacture_date.ToString("dd/MM/yyyy")</dd>

                                <dt class="col-sm-4">Ngày hết hạn:</dt>
                                <dd class="col-sm-8">@Model.expiry_date.ToString("dd/MM/yyyy")</dd>

                                <dt class="col-sm-4">Ngày tạo:</dt>
                                <dd class="col-sm-8">@Model.created_date.ToString("dd/MM/yyyy HH:mm")</dd>
                            </dl>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.notes))
                    {
                        <div class="row">
                            <div class="col-md-12">
                                <dt>Ghi chú:</dt>
                                <dd>@Html.DisplayFor(model => model.notes)</dd>
                            </div>
                        </div>
                    }
                </div>
            </div>

            <form asp-action="Delete" method="post" class="mt-3">
                <input type="hidden" asp-for="medication_id" />
                <button type="submit" class="btn btn-danger">Xác nhận xóa</button>
                <a asp-action="Index" class="btn btn-secondary">Hủy</a>
                <a asp-action="Details" asp-route-id="@Model.medication_id" class="btn btn-info">Chi tiết</a>
            </form>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-md-12 text-left">
            © 2025 - Student's ID - Your Name
        </div>
    </div>
</div>
