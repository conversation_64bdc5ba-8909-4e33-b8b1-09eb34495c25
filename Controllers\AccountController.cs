using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Midterm1212.Models;
using Midterm1212.Models.ViewModels;

namespace Midterm1212.Controllers
{
    public class AccountController : Controller
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly MedicalDbContext _context;

        public AccountController(
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager,
            RoleManager<IdentityRole> roleManager,
            MedicalDbContext context)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _roleManager = roleManager;
            _context = context;
        }

        [HttpGet]
        public IActionResult Login(string? returnUrl = null)
        {
            ViewData["ReturnUrl"] = returnUrl;
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Login(LoginViewModel model, string? returnUrl = null)
        {
            ViewData["ReturnUrl"] = returnUrl;

            if (ModelState.IsValid)
            {
                var user = await _userManager.FindByNameAsync(model.UserName);
                if (user != null && user.IsActive)
                {
                    // Check if user type matches if specified
                    if (model.UserType.HasValue && user.UserType != model.UserType.Value)
                    {
                        ModelState.AddModelError(string.Empty, "Vai trò đăng nhập không đúng.");
                        return View(model);
                    }

                    var result = await _signInManager.PasswordSignInAsync(model.UserName, model.Password, model.RememberMe, lockoutOnFailure: false);
                    if (result.Succeeded)
                    {
                        // Update last login date
                        user.LastLoginDate = DateTime.Now;
                        await _userManager.UpdateAsync(user);

                        // Redirect based on role
                        if (!string.IsNullOrEmpty(returnUrl) && Url.IsLocalUrl(returnUrl))
                        {
                            return Redirect(returnUrl);
                        }

                        return RedirectToAction("Index", "Dashboard");
                    }
                }

                ModelState.AddModelError(string.Empty, "Tên đăng nhập hoặc mật khẩu không đúng.");
            }

            return View(model);
        }

        [HttpGet]
        public async Task<IActionResult> Register()
        {
            // Check if patient ID is available
            var maxPatientId = await _context.Patients.AnyAsync() 
                ? await _context.Patients.MaxAsync(p => p.patient_id) 
                : 0;

            var model = new RegisterPatientViewModel
            {
                PatientId = maxPatientId + 1
            };

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Register(RegisterPatientViewModel model)
        {
            if (ModelState.IsValid)
            {
                // Check if patient ID already exists
                var existingPatient = await _context.Patients.FindAsync(model.PatientId);
                if (existingPatient != null)
                {
                    ModelState.AddModelError("PatientId", "Mã bệnh nhân đã tồn tại.");
                    return View(model);
                }

                // Check if username already exists
                var existingUser = await _userManager.FindByNameAsync(model.UserName);
                if (existingUser != null)
                {
                    ModelState.AddModelError("UserName", "Tên đăng nhập đã tồn tại.");
                    return View(model);
                }

                // Create Patient record
                var patient = new Patient
                {
                    patient_id = model.PatientId,
                    name = model.Name,
                    dob = model.DateOfBirth,
                    gender = model.Gender,
                    phone = model.Phone,
                    address = model.Address
                };

                _context.Patients.Add(patient);
                await _context.SaveChangesAsync();

                // Create ApplicationUser
                var user = new ApplicationUser
                {
                    UserName = model.UserName,
                    Email = model.Email,
                    UserType = UserType.Patient,
                    PatientId = model.PatientId,
                    FullName = model.Name,
                    PhoneNumber = model.Phone
                };

                var result = await _userManager.CreateAsync(user, model.Password);
                if (result.Succeeded)
                {
                    // Add to Patient role
                    await _userManager.AddToRoleAsync(user, "Patient");

                    TempData["SuccessMessage"] = "Đăng ký thành công! Vui lòng đăng nhập.";
                    return RedirectToAction("Login");
                }

                // If user creation failed, remove the patient record
                _context.Patients.Remove(patient);
                await _context.SaveChangesAsync();

                foreach (var error in result.Errors)
                {
                    ModelState.AddModelError(string.Empty, error.Description);
                }
            }

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Logout()
        {
            await _signInManager.SignOutAsync();
            return RedirectToAction("Index", "Home");
        }

        [HttpGet]
        [Authorize]
        public IActionResult ChangePassword()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize]
        public async Task<IActionResult> ChangePassword(ChangePasswordViewModel model)
        {
            if (ModelState.IsValid)
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null)
                {
                    return RedirectToAction("Login");
                }

                var result = await _userManager.ChangePasswordAsync(user, model.CurrentPassword, model.NewPassword);
                if (result.Succeeded)
                {
                    await _signInManager.RefreshSignInAsync(user);
                    TempData["SuccessMessage"] = "Đổi mật khẩu thành công!";
                    return RedirectToAction("Index", "Dashboard");
                }

                foreach (var error in result.Errors)
                {
                    ModelState.AddModelError(string.Empty, error.Description);
                }
            }

            return View(model);
        }

        [HttpGet]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> CreateDoctorAccount()
        {
            // Get doctors without user accounts
            var doctorsWithoutAccounts = await _context.Doctors
                .Where(d => !_context.Users.Any(u => u.DoctorId == d.doctor_id))
                .Select(d => new { d.doctor_id, DisplayText = $"{d.name} - {d.phone}" })
                .ToListAsync();

            ViewBag.DoctorId = new SelectList(doctorsWithoutAccounts, "doctor_id", "DisplayText");

            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> CreateDoctorAccount(CreateDoctorAccountViewModel model)
        {
            if (ModelState.IsValid)
            {
                var doctor = await _context.Doctors.FindAsync(model.DoctorId);
                if (doctor == null)
                {
                    ModelState.AddModelError("DoctorId", "Bác sĩ không tồn tại.");
                    await LoadDoctorDropdown();
                    return View(model);
                }

                // Check if username already exists
                var existingUser = await _userManager.FindByNameAsync(model.UserName);
                if (existingUser != null)
                {
                    ModelState.AddModelError("UserName", "Tên đăng nhập đã tồn tại.");
                    await LoadDoctorDropdown();
                    return View(model);
                }

                var user = new ApplicationUser
                {
                    UserName = model.UserName,
                    Email = model.Email,
                    UserType = UserType.Doctor,
                    DoctorId = model.DoctorId,
                    FullName = doctor.name,
                    PhoneNumber = doctor.phone
                };

                var result = await _userManager.CreateAsync(user, model.Password);
                if (result.Succeeded)
                {
                    await _userManager.AddToRoleAsync(user, "Doctor");
                    TempData["SuccessMessage"] = $"Tạo tài khoản bác sĩ {doctor.name} thành công!";
                    return RedirectToAction("ManageUsers");
                }

                foreach (var error in result.Errors)
                {
                    ModelState.AddModelError(string.Empty, error.Description);
                }
            }

            await LoadDoctorDropdown();
            return View(model);
        }

        [HttpGet]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> ManageUsers()
        {
            var users = await _userManager.Users
                .Include(u => u.Patient)
                .Include(u => u.Doctor)
                .Select(u => new UserManagementViewModel
                {
                    Id = u.Id,
                    UserName = u.UserName!,
                    Email = u.Email!,
                    UserType = u.UserType,
                    FullName = u.FullName,
                    IsActive = u.IsActive,
                    CreatedDate = u.CreatedDate,
                    LastLoginDate = u.LastLoginDate,
                    AssociatedName = u.UserType == UserType.Patient ? u.Patient!.name : 
                                   u.UserType == UserType.Doctor ? u.Doctor!.name : "Admin"
                })
                .ToListAsync();

            return View(users);
        }

        private async Task LoadDoctorDropdown()
        {
            var doctorsWithoutAccounts = await _context.Doctors
                .Where(d => !_context.Users.Any(u => u.DoctorId == d.doctor_id))
                .Select(d => new { d.doctor_id, DisplayText = $"{d.name} - {d.phone}" })
                .ToListAsync();

            ViewBag.DoctorId = new SelectList(doctorsWithoutAccounts, "doctor_id", "DisplayText");
        }
    }
}
