@model IEnumerable<Midterm1212.Models.Medication>

@{
    ViewData["Title"] = "Quản lý thuốc";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1><PERSON><PERSON><PERSON><PERSON> lý thuốc</h1>
            
            <!-- Search and Filter Form -->
            <form method="get" class="mb-3">
                <div class="row">
                    <div class="col-md-4">
                        <input type="text" name="searchString" value="@ViewData["CurrentFilter"]" 
                               class="form-control" placeholder="Tìm kiếm theo tên thuốc, ho<PERSON><PERSON> chất, nhà sản xuất..." />
                    </div>
                    <div class="col-md-3">
                        <select name="categoryId" class="form-control">
                            <option value="">-- Tất cả danh mục --</option>
                            @foreach (var category in ViewBag.Categories)
                            {
                                <option value="@category.Value" selected="@(category.Value == ViewData["CurrentCategory"]?.ToString())">
                                    @category.Text
                                </option>
                            }
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary">Tìm kiếm</button>
                    </div>
                    <div class="col-md-3 text-right">
                        <a asp-action="Create" class="btn btn-success">Thêm thuốc mới</a>
                        <a asp-action="LowStock" class="btn btn-warning">Tồn kho thấp</a>
                        <a asp-action="ExpiringSoon" class="btn btn-danger">Sắp hết hạn</a>
                    </div>
                </div>
            </form>

            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead class="thead-dark">
                        <tr>
                            <th>Mã thuốc</th>
                            <th>Tên thuốc</th>
                            <th>Hoạt chất</th>
                            <th>Liều lượng</th>
                            <th>Dạng thuốc</th>
                            <th>Danh mục</th>
                            <th>Tồn kho</th>
                            <th>Giá bán</th>
                            <th>Hạn sử dụng</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr class="@(item.stock_quantity <= item.minimum_stock ? "table-warning" : "") 
                                      @(item.expiry_date <= DateTime.Now.AddDays(30) ? "table-danger" : "")">
                                <td>@Html.DisplayFor(modelItem => item.medication_id)</td>
                                <td>@Html.DisplayFor(modelItem => item.medication_name)</td>
                                <td>@Html.DisplayFor(modelItem => item.active_ingredient)</td>
                                <td>@Html.DisplayFor(modelItem => item.dosage)</td>
                                <td>@Html.DisplayFor(modelItem => item.form)</td>
                                <td>@Html.DisplayFor(modelItem => item.Category.category_name)</td>
                                <td>
                                    <span class="@(item.stock_quantity <= item.minimum_stock ? "text-warning font-weight-bold" : "")">
                                        @Html.DisplayFor(modelItem => item.stock_quantity)
                                    </span>
                                    <small class="text-muted">/ @item.minimum_stock</small>
                                </td>
                                <td>@item.unit_price.ToString("N0") VNĐ</td>
                                <td>
                                    <span class="@(item.expiry_date <= DateTime.Now.AddDays(30) ? "text-danger font-weight-bold" : "")">
                                        @item.expiry_date.ToString("dd/MM/yyyy")
                                    </span>
                                </td>
                                <td>
                                    @if (item.stock_quantity == 0)
                                    {
                                        <span class="badge badge-danger">Hết hàng</span>
                                    }
                                    else if (item.stock_quantity <= item.minimum_stock)
                                    {
                                        <span class="badge badge-warning">Tồn kho thấp</span>
                                    }
                                    else if (item.expiry_date <= DateTime.Now.AddDays(7))
                                    {
                                        <span class="badge badge-danger">Sắp hết hạn</span>
                                    }
                                    else if (item.expiry_date <= DateTime.Now.AddDays(30))
                                    {
                                        <span class="badge badge-warning">Gần hết hạn</span>
                                    }
                                    else
                                    {
                                        <span class="badge badge-success">Bình thường</span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@item.medication_id" class="btn btn-info btn-sm">Chi tiết</a>
                                        <a asp-action="Edit" asp-route-id="@item.medication_id" class="btn btn-primary btn-sm">Sửa</a>
                                        <a asp-action="Delete" asp-route-id="@item.medication_id" class="btn btn-danger btn-sm">Xóa</a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            @if (!Model.Any())
            {
                <div class="alert alert-info text-center">
                    <h4>Không tìm thấy thuốc nào</h4>
                    <p>Hãy thử thay đổi tiêu chí tìm kiếm hoặc <a asp-action="Create">thêm thuốc mới</a>.</p>
                </div>
            }
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-md-12 text-left">
            © 2025 - Student's ID - Your Name
        </div>
    </div>
</div>

<style>
    .table-warning {
        background-color: #fff3cd !important;
    }
    .table-danger {
        background-color: #f8d7da !important;
    }
    .btn-group .btn {
        margin-right: 2px;
    }
</style>
