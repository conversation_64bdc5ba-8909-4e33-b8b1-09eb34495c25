@model Midterm1212.Models.Medication

@{
    ViewData["Title"] = "Chi tiết thuốc";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1>Chi tiết thuốc</h1>

            <div class="card">
                <div class="card-header">
                    <h3>@Html.DisplayFor(model => model.medication_name)</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Mã thuốc:</dt>
                                <dd class="col-sm-8">@Html.DisplayFor(model => model.medication_id)</dd>

                                <dt class="col-sm-4">Tên thuốc:</dt>
                                <dd class="col-sm-8">@Html.DisplayFor(model => model.medication_name)</dd>

                                <dt class="col-sm-4">Thành phần hoạt chất:</dt>
                                <dd class="col-sm-8">@Html.DisplayFor(model => model.active_ingredient)</dd>

                                <dt class="col-sm-4">Liều lượng:</dt>
                                <dd class="col-sm-8">@Html.DisplayFor(model => model.dosage)</dd>

                                <dt class="col-sm-4">Dạng thuốc:</dt>
                                <dd class="col-sm-8">@Html.DisplayFor(model => model.form)</dd>

                                <dt class="col-sm-4">Nhà sản xuất:</dt>
                                <dd class="col-sm-8">@Html.DisplayFor(model => model.manufacturer)</dd>

                                <dt class="col-sm-4">Danh mục:</dt>
                                <dd class="col-sm-8">@Html.DisplayFor(model => model.Category.category_name)</dd>
                            </dl>
                        </div>

                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Số lượng tồn kho:</dt>
                                <dd class="col-sm-8">
                                    <span class="@(Model.stock_quantity <= Model.minimum_stock ? "text-warning font-weight-bold" : "")">
                                        @Html.DisplayFor(model => model.stock_quantity)
                                    </span>
                                    @if (Model.stock_quantity <= Model.minimum_stock)
                                    {
                                        <span class="badge badge-warning ml-2">Tồn kho thấp</span>
                                    }
                                </dd>

                                <dt class="col-sm-4">Số lượng tối thiểu:</dt>
                                <dd class="col-sm-8">@Html.DisplayFor(model => model.minimum_stock)</dd>

                                <dt class="col-sm-4">Giá bán:</dt>
                                <dd class="col-sm-8">@Model.unit_price.ToString("N0") VNĐ</dd>

                                <dt class="col-sm-4">Ngày sản xuất:</dt>
                                <dd class="col-sm-8">@Model.manufacture_date.ToString("dd/MM/yyyy")</dd>

                                <dt class="col-sm-4">Ngày hết hạn:</dt>
                                <dd class="col-sm-8">
                                    <span class="@(Model.expiry_date <= DateTime.Now.AddDays(30) ? "text-danger font-weight-bold" : "")">
                                        @Model.expiry_date.ToString("dd/MM/yyyy")
                                    </span>
                                    @if (Model.expiry_date <= DateTime.Now.AddDays(7))
                                    {
                                        <span class="badge badge-danger ml-2">Sắp hết hạn</span>
                                    }
                                    else if (Model.expiry_date <= DateTime.Now.AddDays(30))
                                    {
                                        <span class="badge badge-warning ml-2">Gần hết hạn</span>
                                    }
                                </dd>

                                <dt class="col-sm-4">Ngày tạo:</dt>
                                <dd class="col-sm-8">@Model.created_date.ToString("dd/MM/yyyy HH:mm")</dd>

                                @if (Model.updated_date.HasValue)
                                {
                                    <dt class="col-sm-4">Ngày cập nhật:</dt>
                                    <dd class="col-sm-8">@Model.updated_date.Value.ToString("dd/MM/yyyy HH:mm")</dd>
                                }
                            </dl>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.notes))
                    {
                        <div class="row">
                            <div class="col-md-12">
                                <dt>Ghi chú:</dt>
                                <dd>@Html.DisplayFor(model => model.notes)</dd>
                            </div>
                        </div>
                    }
                </div>
            </div>

            <!-- Inventory Alerts -->
            @if (Model.InventoryAlerts.Any())
            {
                <div class="card mt-3">
                    <div class="card-header">
                        <h4>Cảnh báo tồn kho</h4>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Loại cảnh báo</th>
                                        <th>Mức độ</th>
                                        <th>Thông điệp</th>
                                        <th>Trạng thái</th>
                                        <th>Ngày tạo</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var alert in Model.InventoryAlerts.OrderByDescending(a => a.created_date))
                                    {
                                        <tr>
                                            <td>@alert.alert_type</td>
                                            <td>
                                                <span class="badge @(alert.priority_level == "Khẩn cấp" ? "badge-danger" : 
                                                                   alert.priority_level == "Cao" ? "badge-warning" : "badge-info")">
                                                    @alert.priority_level
                                                </span>
                                            </td>
                                            <td>@alert.message</td>
                                            <td>
                                                <span class="badge @(alert.status == "Chưa xử lý" ? "badge-warning" : 
                                                                   alert.status == "Đã xử lý" ? "badge-success" : "badge-secondary")">
                                                    @alert.status
                                                </span>
                                            </td>
                                            <td>@alert.created_date.ToString("dd/MM/yyyy HH:mm")</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }

            <div class="mt-3">
                <a asp-action="Edit" asp-route-id="@Model.medication_id" class="btn btn-primary">Chỉnh sửa</a>
                <a asp-action="Index" class="btn btn-secondary">Quay lại danh sách</a>
                <a asp-action="Delete" asp-route-id="@Model.medication_id" class="btn btn-danger">Xóa</a>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-md-12 text-left">
            © 2025 - Student's ID - Your Name
        </div>
    </div>
</div>
