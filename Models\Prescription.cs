using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Midterm1212.Models
{
    public class Prescription
    {
        public Prescription()
        {
            PrescriptionItems = new HashSet<PrescriptionItem>();
            MedicationOrders = new HashSet<MedicationOrder>();
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        [Required]
        public int prescription_id { get; set; }

        [Required]
        [Display(Name = "Bệnh nhân")]
        public int patient_id { get; set; }

        [Required]
        [Display(Name = "<PERSON>á<PERSON> sĩ")]
        public int doctor_id { get; set; }

        [Display(Name = "<PERSON><PERSON> sơ y tế")]
        public int? medical_record_id { get; set; }

        [Required]
        [Display(Name = "<PERSON>ày kê đơn")]
        [DataType(DataType.Date)]
        public DateTime prescription_date { get; set; } = DateTime.Now;

        [Required]
        [Display(Name = "Trạng thái")]
        public string status { get; set; } = "Chờ xử lý"; // <PERSON><PERSON> xử lý, <PERSON><PERSON> cấp thuốc, <PERSON><PERSON><PERSON> thành, <PERSON><PERSON><PERSON>

        [Display(Name = "<PERSON><PERSON> chú")]
        public string? notes { get; set; }

        [Required]
        [Column(TypeName = "decimal(12,2)")]
        [Display(Name = "Tổng tiền")]
        public decimal total_amount { get; set; }

        [Required]
        [Display(Name = "Ngày tạo")]
        public DateTime created_date { get; set; } = DateTime.Now;

        [Display(Name = "Ngày cập nhật")]
        public DateTime? updated_date { get; set; }

        [Display(Name = "Người cập nhật")]
        public string? updated_by { get; set; }

        [ForeignKey("patient_id")]
        public virtual Patient Patient { get; set; }

        [ForeignKey("doctor_id")]
        public virtual Doctor Doctor { get; set; }

        [ForeignKey("medical_record_id")]
        public virtual MedicalRecord? MedicalRecord { get; set; }

        public virtual ICollection<PrescriptionItem> PrescriptionItems { get; set; }
        public virtual ICollection<MedicationOrder> MedicationOrders { get; set; }
    }
}
