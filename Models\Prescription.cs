using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Midterm1212.Models
{
    public class Prescription
    {
        public Prescription()
        {
            PrescriptionDetails = new HashSet<PrescriptionDetail>();
            MedicationOrders = new HashSet<MedicationOrder>();
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        [Required]
        public int Id { get; set; }

        [Required]
        [Display(Name = "Mã hồ sơ y tế")]
        public int MedicalRecordId { get; set; }

        [Required]
        [Display(Name = "Ngày kê đơn")]
        [DataType(DataType.Date)]
        public DateTime PrescriptionDate { get; set; }

        [Display(Name = "Hướng dẫn sử dụng")]
        public string? Instructions { get; set; }

        [Display(Name = "Đã thanh toán")]
        public bool IsPaid { get; set; } = false;

        [Display(Name = "Tổng chi phí")]
        [Column(TypeName = "decimal(12,2)")]
        public decimal TotalCost { get; set; }

        [Display(Name = "Trạng thái")]
        public string Status { get; set; } = "Chờ xử lý";

        [Display(Name = "Ngày tạo")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "Ghi chú")]
        public string? Notes { get; set; }

        [ForeignKey("MedicalRecordId")]
        public virtual MedicalRecord MedicalRecord { get; set; }

        public virtual ICollection<PrescriptionDetail> PrescriptionDetails { get; set; }
        public virtual ICollection<MedicationOrder> MedicationOrders { get; set; }
    }
}
