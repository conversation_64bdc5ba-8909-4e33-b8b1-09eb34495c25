using Midterm1212.Models;

namespace Midterm1212.Models.ViewModels
{
    public class AdminDashboardViewModel
    {
        public int TotalPatients { get; set; }
        public int TotalDoctors { get; set; }
        public int TotalAppointments { get; set; }
        public int TotalMedicines { get; set; }
        public int TotalPrescriptions { get; set; }
        public int TotalMedicationOrders { get; set; }
        public int LowStockMedicines { get; set; }
        public int ExpiredMedicines { get; set; }
        public int PendingAppointments { get; set; }
        public int PendingOrders { get; set; }
        public List<Appointment> RecentAppointments { get; set; } = new List<Appointment>();
        public List<MedicationOrder> RecentOrders { get; set; } = new List<MedicationOrder>();
        public decimal MonthlyRevenue { get; set; }
        public int TotalUsers { get; set; }
        public int ActiveUsers { get; set; }
    }
} 