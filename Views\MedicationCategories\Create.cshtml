@model Midterm1212.Models.MedicationCategory

@{
    ViewData["Title"] = "Thêm danh mục thuốc mới";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1>Thêm danh mục thuốc mới</h1>

            <form asp-action="Create" method="post">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="category_id" class="control-label">Mã danh mục</label>
                            <input asp-for="category_id" class="form-control" />
                            <span asp-validation-for="category_id" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="category_name" class="control-label">Tên danh mục</label>
                            <input asp-for="category_name" class="form-control" />
                            <span asp-validation-for="category_name" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="description" class="control-label">Mô tả</label>
                            <textarea asp-for="description" class="form-control" rows="4" placeholder="Mô tả về danh mục thuốc này..."></textarea>
                            <span asp-validation-for="description" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Gợi ý danh mục thuốc</h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li><strong>Thuốc giảm đau:</strong> Paracetamol, Ibuprofen, Aspirin...</li>
                                    <li><strong>Kháng sinh:</strong> Amoxicillin, Cephalexin, Azithromycin...</li>
                                    <li><strong>Thuốc tim mạch:</strong> Thuốc huyết áp, thuốc tim...</li>
                                    <li><strong>Thuốc tiêu hóa:</strong> Thuốc dạ dày, thuốc tiêu hóa...</li>
                                    <li><strong>Thuốc hô hấp:</strong> Thuốc ho, thuốc hen suyễn...</li>
                                    <li><strong>Thuốc da liễu:</strong> Thuốc bôi, kem dưỡng da...</li>
                                    <li><strong>Vitamin & khoáng chất:</strong> Vitamin tổng hợp...</li>
                                    <li><strong>Thuốc thần kinh:</strong> Thuốc an thần, chống trầm cảm...</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <input type="submit" value="Lưu" class="btn btn-primary" />
                    <a asp-action="Index" class="btn btn-secondary">Quay lại</a>
                </div>
            </form>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-md-12 text-left">
            © 2025 - Student's ID - Your Name
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
