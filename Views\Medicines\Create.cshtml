@model Midterm1212.Models.Medicine

@{
    ViewData["Title"] = "Thêm thuốc mới";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">@ViewData["Title"]</h4>
                </div>
                <div class="card-body">
                    <form asp-action="Create">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Id" class="form-label"></label>
                                    <input asp-for="Id" class="form-control" />
                                    <span asp-validation-for="Id" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Name" class="form-label"></label>
                                    <input asp-for="Name" class="form-control" />
                                    <span asp-validation-for="Name" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Description" class="form-label"></label>
                            <textarea asp-for="Description" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Manufacturer" class="form-label"></label>
                                    <input asp-for="Manufacturer" class="form-control" />
                                    <span asp-validation-for="Manufacturer" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Form" class="form-label"></label>
                                    <select asp-for="Form" class="form-select">
                                        <option value="">-- Chọn dạng thuốc --</option>
                                        <option value="Viên nén">Viên nén</option>
                                        <option value="Viên nang">Viên nang</option>
                                        <option value="Siro">Siro</option>
                                        <option value="Thuốc tiêm">Thuốc tiêm</option>
                                        <option value="Thuốc bôi">Thuốc bôi</option>
                                        <option value="Thuốc nhỏ mắt">Thuốc nhỏ mắt</option>
                                        <option value="Thuốc xịt">Thuốc xịt</option>
                                        <option value="Khác">Khác</option>
                                    </select>
                                    <span asp-validation-for="Form" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Dosage" class="form-label"></label>
                                    <input asp-for="Dosage" class="form-control" placeholder="VD: 500mg, 10ml..." />
                                    <span asp-validation-for="Dosage" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Price" class="form-label"></label>
                                    <div class="input-group">
                                        <input asp-for="Price" class="form-control" step="0.01" />
                                        <span class="input-group-text">VNĐ</span>
                                    </div>
                                    <span asp-validation-for="Price" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="QuantityInStock" class="form-label"></label>
                                    <input asp-for="QuantityInStock" class="form-control" />
                                    <span asp-validation-for="QuantityInStock" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="LowStockThreshold" class="form-label"></label>
                                    <input asp-for="LowStockThreshold" class="form-control" value="10" />
                                    <span asp-validation-for="LowStockThreshold" class="text-danger"></span>
                                    <div class="form-text">Số lượng tối thiểu để cảnh báo tồn kho thấp</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="ExpirationDate" class="form-label"></label>
                            <input asp-for="ExpirationDate" class="form-control" type="date" />
                            <span asp-validation-for="ExpirationDate" class="text-danger"></span>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Lưu thuốc
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Set minimum date for expiration date to today
        document.getElementById('ExpirationDate').min = new Date().toISOString().split('T')[0];
        
        // Auto-format price input
        document.getElementById('Price').addEventListener('input', function(e) {
            let value = e.target.value;
            if (value && !isNaN(value)) {
                e.target.value = parseFloat(value).toFixed(2);
            }
        });
    </script>
}
