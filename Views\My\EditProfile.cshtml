@model Midterm1212.Models.Patient

@{
    ViewData["Title"] = "Chỉnh sửa hồ sơ cá nhân";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-user-edit"></i> @ViewData["Title"]
                    </h4>
                </div>
                <div class="card-body">
                    <form asp-action="EditProfile" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <input type="hidden" asp-for="patient_id" />
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Mã bệnh nhân</label>
                                    <input value="@Model.patient_id" class="form-control" readonly />
                                    <div class="form-text"><PERSON><PERSON> bệnh nhân không thể thay đổi</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="name" class="form-label">Họ và tên</label>
                                    <input asp-for="name" class="form-control" />
                                    <span asp-validation-for="name" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="dob" class="form-label">Ngày sinh</label>
                                    <input asp-for="dob" class="form-control" type="date" />
                                    <span asp-validation-for="dob" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="gender" class="form-label">Giới tính</label>
                                    <select asp-for="gender" class="form-select">
                                        <option value="">-- Chọn giới tính --</option>
                                        <option value="Nam">Nam</option>
                                        <option value="Nữ">Nữ</option>
                                        <option value="Khác">Khác</option>
                                    </select>
                                    <span asp-validation-for="gender" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="phone" class="form-label">Số điện thoại</label>
                                    <input asp-for="phone" class="form-control" />
                                    <span asp-validation-for="phone" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Tuổi</label>
                                    <input value="@((DateTime.Now - Model.dob).Days / 365) tuổi" class="form-control" readonly />
                                    <div class="form-text">Tuổi được tính tự động từ ngày sinh</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="address" class="form-label">Địa chỉ</label>
                            <textarea asp-for="address" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="address" class="text-danger"></span>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Lưu ý:</h6>
                            <ul class="mb-0">
                                <li>Thông tin cá nhân sẽ được sử dụng cho việc liên lạc và khám bệnh</li>
                                <li>Vui lòng cung cấp thông tin chính xác để đảm bảo chất lượng dịch vụ</li>
                                <li>Số điện thoại sẽ được sử dụng để thông báo về lịch hẹn</li>
                            </ul>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-action="Profile" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại hồ sơ
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save"></i> Cập nhật thông tin
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Set max date for date of birth to today
        document.getElementById('dob').max = new Date().toISOString().split('T')[0];
        
        // Update age when date of birth changes
        document.getElementById('dob').addEventListener('change', function() {
            const dob = new Date(this.value);
            const today = new Date();
            const age = Math.floor((today - dob) / (365.25 * 24 * 60 * 60 * 1000));
            
            // Find the age input field and update it
            const ageInputs = document.querySelectorAll('input[readonly]');
            ageInputs.forEach(input => {
                if (input.value.includes('tuổi')) {
                    input.value = age + ' tuổi';
                }
            });
        });
    </script>
}
