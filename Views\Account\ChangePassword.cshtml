@model Midterm1212.Models.ViewModels.ChangePasswordViewModel

@{
    ViewData["Title"] = "Đổi mật khẩu";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-key"></i> @ViewData["Title"]
                    </h4>
                </div>
                <div class="card-body">
                    <form asp-action="ChangePassword" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <div class="mb-3">
                            <label asp-for="CurrentPassword" class="form-label"></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input asp-for="CurrentPassword" class="form-control" placeholder="Nhập mật khẩu hiện tại" />
                            </div>
                            <span asp-validation-for="CurrentPassword" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="NewPassword" class="form-label"></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-key"></i></span>
                                <input asp-for="NewPassword" class="form-control" placeholder="Nhập mật khẩu mới" />
                            </div>
                            <span asp-validation-for="NewPassword" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="ConfirmPassword" class="form-label"></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-check"></i></span>
                                <input asp-for="ConfirmPassword" class="form-control" placeholder="Nhập lại mật khẩu mới" />
                            </div>
                            <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Yêu cầu mật khẩu:</h6>
                            <ul class="mb-0">
                                <li>Ít nhất 6 ký tự</li>
                                <li>Nên bao gồm chữ hoa, chữ thường và số</li>
                                <li>Không sử dụng thông tin cá nhân dễ đoán</li>
                            </ul>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" asp-controller="Dashboard" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save"></i> Đổi mật khẩu
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Password strength indicator
        document.getElementById('NewPassword').addEventListener('input', function() {
            const password = this.value;
            const strengthDiv = document.getElementById('password-strength');
            
            if (!strengthDiv) {
                const div = document.createElement('div');
                div.id = 'password-strength';
                div.className = 'mt-2';
                this.parentNode.parentNode.appendChild(div);
            }
            
            let strength = 0;
            let message = '';
            let className = '';
            
            if (password.length >= 6) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;
            
            switch (strength) {
                case 0:
                case 1:
                    message = 'Mật khẩu yếu';
                    className = 'text-danger';
                    break;
                case 2:
                case 3:
                    message = 'Mật khẩu trung bình';
                    className = 'text-warning';
                    break;
                case 4:
                case 5:
                    message = 'Mật khẩu mạnh';
                    className = 'text-success';
                    break;
            }
            
            document.getElementById('password-strength').innerHTML = 
                `<small class="${className}"><i class="fas fa-shield-alt"></i> ${message}</small>`;
        });
    </script>
}
