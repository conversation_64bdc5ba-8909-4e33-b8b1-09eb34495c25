@model IEnumerable<Midterm1212.Models.Medication>

@{
    ViewData["Title"] = "Thuốc sắp hết hạn";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1><PERSON><PERSON><PERSON><PERSON> sắp hết hạn</h1>
            
            <div class="alert alert-danger">
                <h4><i class="fas fa-clock"></i> Cảnh báo hết hạn</h4>
                <p>Danh sách các thuốc sắp hết hạn trong vòng 30 ngày tới. Vui lòng kiểm tra và xử lý kịp thời.</p>
            </div>

            <p>
                <a asp-action="Index" class="btn btn-secondary">Quay lại danh sách thuốc</a>
                <a asp-action="LowStock" class="btn btn-warning">Tồn kho thấp</a>
            </p>

            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead class="thead-dark">
                        <tr>
                            <th><PERSON><PERSON> thuốc</th>
                            <th>Tên thuốc</th>
                            <th>Danh mục</th>
                            <th>Tồn kho</th>
                            <th>Ngày hết hạn</th>
                            <th>Số ngày còn lại</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            var daysUntilExpiry = (item.expiry_date - DateTime.Now).Days;
                            <tr class="@(daysUntilExpiry <= 7 ? "table-danger" : "table-warning")">
                                <td>@Html.DisplayFor(modelItem => item.medication_id)</td>
                                <td>@Html.DisplayFor(modelItem => item.medication_name)</td>
                                <td>@Html.DisplayFor(modelItem => item.Category.category_name)</td>
                                <td>@Html.DisplayFor(modelItem => item.stock_quantity)</td>
                                <td>
                                    <span class="font-weight-bold @(daysUntilExpiry <= 7 ? "text-danger" : "text-warning")">
                                        @item.expiry_date.ToString("dd/MM/yyyy")
                                    </span>
                                </td>
                                <td>
                                    <span class="font-weight-bold @(daysUntilExpiry <= 7 ? "text-danger" : "text-warning")">
                                        @if (daysUntilExpiry < 0)
                                        {
                                            <span>Đã hết hạn @Math.Abs(daysUntilExpiry) ngày</span>
                                        }
                                        else if (daysUntilExpiry == 0)
                                        {
                                            <span>Hết hạn hôm nay</span>
                                        }
                                        else
                                        {
                                            <span>@daysUntilExpiry ngày</span>
                                        }
                                    </span>
                                </td>
                                <td>
                                    @if (daysUntilExpiry < 0)
                                    {
                                        <span class="badge badge-dark">Đã hết hạn</span>
                                    }
                                    else if (daysUntilExpiry <= 7)
                                    {
                                        <span class="badge badge-danger">Khẩn cấp</span>
                                    }
                                    else if (daysUntilExpiry <= 30)
                                    {
                                        <span class="badge badge-warning">Sắp hết hạn</span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@item.medication_id" class="btn btn-info btn-sm">Chi tiết</a>
                                        <a asp-action="Edit" asp-route-id="@item.medication_id" class="btn btn-primary btn-sm">Cập nhật</a>
                                        @if (daysUntilExpiry < 0 || item.stock_quantity == 0)
                                        {
                                            <a asp-action="Delete" asp-route-id="@item.medication_id" class="btn btn-danger btn-sm">Loại bỏ</a>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            @if (!Model.Any())
            {
                <div class="alert alert-success text-center">
                    <h4><i class="fas fa-check-circle"></i> Tuyệt vời!</h4>
                    <p>Hiện tại không có thuốc nào sắp hết hạn trong vòng 30 ngày tới.</p>
                    <a asp-action="Index" class="btn btn-primary">Quay lại danh sách thuốc</a>
                </div>
            }
            else
            {
                <div class="alert alert-info">
                    <strong>Tổng số thuốc sắp hết hạn:</strong> @Model.Count() thuốc
                    <br />
                    <strong>Đã hết hạn:</strong> @Model.Count(m => (m.expiry_date - DateTime.Now).Days < 0) thuốc
                    <br />
                    <strong>Hết hạn trong 7 ngày:</strong> @Model.Count(m => (m.expiry_date - DateTime.Now).Days >= 0 && (m.expiry_date - DateTime.Now).Days <= 7) thuốc
                    <br />
                    <strong>Hết hạn trong 30 ngày:</strong> @Model.Count(m => (m.expiry_date - DateTime.Now).Days > 7 && (m.expiry_date - DateTime.Now).Days <= 30) thuốc
                </div>
            }
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-md-12 text-left">
            © 2025 - Student's ID - Your Name
        </div>
    </div>
</div>

<style>
    .table-warning {
        background-color: #fff3cd !important;
    }
    .table-danger {
        background-color: #f8d7da !important;
    }
</style>
