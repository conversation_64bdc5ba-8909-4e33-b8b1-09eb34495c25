using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Midterm1212.Models
{
    public enum UserType
    {
        Patient = 1,
        Doctor = 2,
        Admin = 3
    }

    public class ApplicationUser : IdentityUser
    {
        [Required]
        [Display(Name = "Loại người dùng")]
        public UserType UserType { get; set; }

        [Display(Name = "Mã bệnh nhân")]
        public int? PatientId { get; set; }

        [Display(Name = "Mã bác sĩ")]
        public int? DoctorId { get; set; }

        [Display(Name = "Họ và tên")]
        public string? FullName { get; set; }

        [Display(Name = "<PERSON><PERSON><PERSON> tạo")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "Hoạt động")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "Lần đăng nhập cuối")]
        public DateTime? LastLoginDate { get; set; }

        // Navigation properties
        [ForeignKey("PatientId")]
        public virtual Patient? Patient { get; set; }

        [ForeignKey("DoctorId")]
        public virtual Doctor? Doctor { get; set; }
    }
}
