using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Midterm1212.Models
{
    public class PrescriptionDetail
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        [Required]
        public int Id { get; set; }

        [Required]
        [Display(Name = "Mã đơn thuốc")]
        public int PrescriptionId { get; set; }

        [Required]
        [Display(Name = "Mã thuốc")]
        public int MedicineId { get; set; }

        [Required]
        [Display(Name = "Số lượng")]
        public int Quantity { get; set; }

        [Required]
        [Display(Name = "Liều lượng")]
        public string Dosage { get; set; } = string.Empty;

        [Display(Name = "Tần suất sử dụng")]
        public string? Frequency { get; set; }

        [Display(Name = "Thời gian sử dụng")]
        public string? Duration { get; set; }

        [Display(Name = "Hướng dẫn sử dụng")]
        public string? Instructions { get; set; }

        [Display(Name = "Đơn giá")]
        [Column(TypeName = "decimal(10,2)")]
        public decimal UnitPrice { get; set; }

        [Display(Name = "Thành tiền")]
        [Column(TypeName = "decimal(12,2)")]
        public decimal TotalPrice { get; set; }

        [Display(Name = "Ghi chú")]
        public string? Notes { get; set; }

        [ForeignKey("PrescriptionId")]
        public virtual Prescription Prescription { get; set; }

        [ForeignKey("MedicineId")]
        public virtual Medicine Medicine { get; set; }
    }
}
