@model Midterm1212.Models.MedicationOrder

@{
    ViewData["Title"] = "Đặt hàng thành công";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="text-center mb-4">
                <div class="success-icon mb-3">
                    <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                </div>
                <h2 class="text-success">Đặt hàng thành công!</h2>
                <p class="text-muted">Đơn hàng COD của bạn đã được tạo và sẽ được xử lý trong thời gian sớm nhất</p>
            </div>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <div class="row">
                <!-- Order Summary -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-receipt"></i> Thông tin đơn hàng #@Model.Id
                            </h5>
                        </div>
                        <div class="card-body">
                            <dl class="row">
                                <dt class="col-sm-5">Mã đơn hàng:</dt>
                                <dd class="col-sm-7"><strong>#@Model.Id</strong></dd>
                                
                                <dt class="col-sm-5">Ngày đặt:</dt>
                                <dd class="col-sm-7">@Model.OrderDate.ToString("dd/MM/yyyy HH:mm")</dd>
                                
                                <dt class="col-sm-5">Trạng thái:</dt>
                                <dd class="col-sm-7">
                                    <span class="badge bg-warning">@Model.OrderStatus</span>
                                </dd>
                                
                                <dt class="col-sm-5">Phương thức:</dt>
                                <dd class="col-sm-7"><strong>@Model.PaymentMethod</strong></dd>
                                
                                <dt class="col-sm-5">Tổng tiền:</dt>
                                <dd class="col-sm-7"><h5 class="text-success">@Model.TotalAmount.ToString("C")</h5></dd>
                                
                                <dt class="col-sm-5">Dự kiến giao:</dt>
                                <dd class="col-sm-7">
                                    <strong>@Model.EstimatedDeliveryDate?.ToString("dd/MM/yyyy")</strong>
                                    <br><small class="text-muted">Trong 2-3 ngày làm việc</small>
                                </dd>
                            </dl>
                            
                            <hr>
                            
                            <h6>Thông tin giao hàng:</h6>
                            <p><strong>Địa chỉ:</strong> @Model.DeliveryAddress</p>
                            <p><strong>Điện thoại:</strong> @Model.ContactPhone</p>
                            
                            @if (!string.IsNullOrEmpty(Model.Notes))
                            {
                                <p><strong>Ghi chú:</strong> @Model.Notes</p>
                            }
                        </div>
                    </div>
                </div>

                <!-- Prescription Details -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-prescription"></i> Chi tiết đơn thuốc #@Model.Prescription.Id
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <dl class="row">
                                    <dt class="col-sm-5">Bệnh nhân:</dt>
                                    <dd class="col-sm-7"><strong>@Model.Patient.name</strong></dd>
                                    
                                    <dt class="col-sm-5">Bác sĩ kê đơn:</dt>
                                    <dd class="col-sm-7">BS. @Model.Prescription.MedicalRecord.Doctor.name</dd>
                                    
                                    <dt class="col-sm-5">Ngày kê đơn:</dt>
                                    <dd class="col-sm-7">@Model.Prescription.PrescriptionDate.ToString("dd/MM/yyyy")</dd>
                                </dl>
                            </div>

                            <h6>Danh sách thuốc:</h6>
                            @if (Model.Prescription.PrescriptionDetails != null && Model.Prescription.PrescriptionDetails.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Tên thuốc</th>
                                                <th>SL</th>
                                                <th>Thành tiền</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var detail in Model.Prescription.PrescriptionDetails)
                                            {
                                                <tr>
                                                    <td>
                                                        <strong>@detail.Medicine.Name</strong>
                                                        @if (!string.IsNullOrEmpty(detail.Dosage))
                                                        {
                                                            <br><small class="text-muted">@detail.Dosage</small>
                                                        }
                                                        @if (!string.IsNullOrEmpty(detail.Instructions))
                                                        {
                                                            <br><small class="text-info">@detail.Instructions</small>
                                                        }
                                                    </td>
                                                    <td>@detail.Quantity</td>
                                                    <td><strong>@detail.TotalPrice.ToString("C")</strong></td>
                                                </tr>
                                            }
                                        </tbody>
                                        <tfoot>
                                            <tr class="table-success">
                                                <th colspan="2">Tổng cộng:</th>
                                                <th>@Model.Prescription.TotalCost.ToString("C")</th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            }

                            @if (!string.IsNullOrEmpty(Model.Prescription.Instructions))
                            {
                                <div class="alert alert-info mt-3">
                                    <h6><i class="fas fa-info-circle"></i> Hướng dẫn sử dụng:</h6>
                                    <p class="mb-0">@Model.Prescription.Instructions</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- Next Steps -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-clipboard-list"></i> Các bước tiếp theo
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="step-icon mb-2">
                                            <i class="fas fa-check-circle text-success fa-2x"></i>
                                        </div>
                                        <h6>1. Đặt hàng</h6>
                                        <p class="text-muted small">Đơn hàng đã được tạo thành công</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="step-icon mb-2">
                                            <i class="fas fa-clock text-warning fa-2x"></i>
                                        </div>
                                        <h6>2. Xác nhận</h6>
                                        <p class="text-muted small">Chúng tôi sẽ xác nhận đơn hàng trong 2-4 giờ</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="step-icon mb-2">
                                            <i class="fas fa-truck text-info fa-2x"></i>
                                        </div>
                                        <h6>3. Giao hàng</h6>
                                        <p class="text-muted small">Thuốc sẽ được giao trong 2-3 ngày</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="step-icon mb-2">
                                            <i class="fas fa-money-bill text-success fa-2x"></i>
                                        </div>
                                        <h6>4. Thanh toán</h6>
                                        <p class="text-muted small">Thanh toán khi nhận hàng</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="d-grid">
                                        <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-primary">
                                            <i class="fas fa-eye"></i><br>
                                            Xem chi tiết đơn hàng
                                        </a>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="d-grid">
                                        <a href="#" class="btn btn-info" onclick="printOrder()">
                                            <i class="fas fa-print"></i><br>
                                            In đơn hàng
                                        </a>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="d-grid">
                                        <a asp-controller="My" asp-action="Prescriptions" class="btn btn-success">
                                            <i class="fas fa-prescription"></i><br>
                                            Đơn thuốc của tôi
                                        </a>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="d-grid">
                                        <a asp-controller="Dashboard" asp-action="Index" class="btn btn-secondary">
                                            <i class="fas fa-home"></i><br>
                                            Về trang chủ
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="alert alert-success">
                        <h6><i class="fas fa-phone"></i> Thông tin liên hệ:</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <strong>Hotline hỗ trợ:</strong><br>
                                <span class="text-primary">1900-xxxx</span>
                            </div>
                            <div class="col-md-4">
                                <strong>Email:</strong><br>
                                <span class="text-primary"><EMAIL></span>
                            </div>
                            <div class="col-md-4">
                                <strong>Giờ làm việc:</strong><br>
                                <span class="text-muted">8:00 - 17:00 (T2-T6)</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function printOrder() {
            window.print();
        }
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
        
        // Success animation
        $(document).ready(function() {
            $('.fa-check-circle').addClass('animate__animated animate__bounceIn');
        });
    </script>
    
    <style>
        @@media print {
            .btn, .alert-success:last-child {
                display: none !important;
            }
            .card {
                border: none !important;
                box-shadow: none !important;
            }
        }
        
        .success-icon {
            animation: bounce 2s infinite;
        }
        
        @@keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
    </style>
}
