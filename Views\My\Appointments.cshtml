@model IEnumerable<Midterm1212.Models.Appointment>

@{
    ViewData["Title"] = "Lịch hẹn của tôi";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h2>@ViewData["Title"]</h2>
                <a asp-action="BookAppointment" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Đặt lịch hẹn mới
                </a>
            </div>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <div class="card">
                <div class="card-header">
                    <form asp-action="Appointments" method="get" class="row g-3">
                        <div class="col-md-4">
                            @Html.DropDownList("StatusFilter", ViewBag.StatusList as SelectList, new { @class = "form-select" })
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-filter"></i> Lọc
                            </button>
                        </div>
                        <div class="col-md-2">
                            <a asp-action="Appointments" class="btn btn-outline-secondary">
                                <i class="fas fa-redo"></i> Làm mới
                            </a>
                        </div>
                    </form>
                </div>
                <div class="card-body">
                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>
                                            <a asp-action="Appointments" asp-route-sortOrder="@ViewData["DateSortParm"]" 
                                               asp-route-statusFilter="@ViewData["StatusFilter"]">
                                                Ngày hẹn <i class="fas fa-sort"></i>
                                            </a>
                                        </th>
                                        <th>Giờ hẹn</th>
                                        <th>Bác sĩ</th>
                                        <th>Chuyên khoa</th>
                                        <th>
                                            <a asp-action="Appointments" asp-route-sortOrder="@ViewData["StatusSortParm"]" 
                                               asp-route-statusFilter="@ViewData["StatusFilter"]">
                                                Trạng thái <i class="fas fa-sort"></i>
                                            </a>
                                        </th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in Model)
                                    {
                                        <tr class="@(item.appointment_date.Date == DateTime.Today ? "table-info" : "")">
                                            <td>
                                                @item.appointment_date.ToString("dd/MM/yyyy")
                                                @if (item.appointment_date.Date == DateTime.Today)
                                                {
                                                    <span class="badge bg-info">Hôm nay</span>
                                                }
                                                else if (item.appointment_date.Date == DateTime.Today.AddDays(1))
                                                {
                                                    <span class="badge bg-warning">Ngày mai</span>
                                                }
                                            </td>
                                            <td>
                                                <strong>@item.appointment_time.ToString(@"hh\:mm")</strong>
                                            </td>
                                            <td>
                                                <strong>BS. @item.Doctor.name</strong><br>
                                                <small class="text-muted">@item.Doctor.phone</small>
                                            </td>
                                            <td>@item.Doctor.specialization</td>
                                            <td>
                                                @switch (item.status)
                                                {
                                                    case "Pending":
                                                        <span class="badge bg-warning">Chờ xác nhận</span>
                                                        break;
                                                    case "Confirmed":
                                                        <span class="badge bg-info">Đã xác nhận</span>
                                                        break;
                                                    case "Complete":
                                                        <span class="badge bg-success">Hoàn thành</span>
                                                        break;
                                                    case "Cancelled":
                                                        <span class="badge bg-danger">Đã hủy</span>
                                                        break;
                                                    default:
                                                        <span class="badge bg-secondary">@item.status</span>
                                                        break;
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-controller="Appointments" asp-action="Details" asp-route-id="@item.appointment_id" 
                                                       class="btn btn-outline-info btn-sm" title="Xem chi tiết">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    @if (item.status == "Pending" && item.appointment_date.Date >= DateTime.Today)
                                                    {
                                                        <button class="btn btn-outline-danger btn-sm" title="Hủy lịch hẹn"
                                                                onclick="cancelAppointment(@item.appointment_id)">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-times fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">Bạn chưa có lịch hẹn nào</h5>
                            <p class="text-muted">Hãy đặt lịch hẹn với bác sĩ để được tư vấn và khám bệnh</p>
                            <a asp-action="BookAppointment" class="btn btn-primary">
                                <i class="fas fa-calendar-plus"></i> Đặt lịch hẹn ngay
                            </a>
                        </div>
                    }
                </div>
            </div>

            <!-- Statistics -->
            @if (Model.Any())
            {
                <div class="row mt-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h4>@Model.Count()</h4>
                                <p class="mb-0">Tổng lịch hẹn</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body text-center">
                                <h4>@Model.Count(a => a.status == "Pending")</h4>
                                <p class="mb-0">Chờ xác nhận</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h4>@Model.Count(a => a.status == "Confirmed")</h4>
                                <p class="mb-0">Đã xác nhận</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h4>@Model.Count(a => a.status == "Complete")</h4>
                                <p class="mb-0">Hoàn thành</p>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function cancelAppointment(appointmentId) {
            if (confirm('Bạn có chắc chắn muốn hủy lịch hẹn này?')) {
                // Implementation for canceling appointment
                // This would typically be a POST request to update the appointment status
                alert('Chức năng hủy lịch hẹn sẽ được triển khai trong phiên bản tiếp theo.');
            }
        }
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    </script>
}
