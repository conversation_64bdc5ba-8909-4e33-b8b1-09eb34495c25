using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Midterm1212.Models;

namespace Midterm1212.Controllers
{
    [Authorize(Roles = "Admin")]
    public class MedicationOrdersController : Controller
    {
        private readonly MedicalDbContext _context;

        public MedicationOrdersController(MedicalDbContext context)
        {
            _context = context;
        }

        public async Task<IActionResult> Index(string searchString, string statusFilter, string sortOrder)
        {
            ViewData["CurrentFilter"] = searchString;
            ViewData["StatusFilter"] = statusFilter;
            ViewData["DateSortParm"] = String.IsNullOrEmpty(sortOrder) ? "date_desc" : "";
            ViewData["StatusSortParm"] = sortOrder == "Status" ? "status_desc" : "Status";
            ViewData["TotalSortParm"] = sortOrder == "Total" ? "total_desc" : "Total";

            var orders = from o in _context.MedicationOrders
                        .Include(o => o.Patient)
                        .Include(o => o.Prescription)
                        .ThenInclude(p => p.PrescriptionDetails)
                        .ThenInclude(pd => pd.Medicine)
                        select o;

            if (!String.IsNullOrEmpty(searchString))
            {
                orders = orders.Where(o => o.Patient.name.Contains(searchString)
                                        || o.ContactPhone.Contains(searchString)
                                        || o.DeliveryAddress.Contains(searchString));
            }

            if (!String.IsNullOrEmpty(statusFilter))
            {
                orders = orders.Where(o => o.OrderStatus == statusFilter);
            }

            switch (sortOrder)
            {
                case "date_desc":
                    orders = orders.OrderByDescending(o => o.OrderDate);
                    break;
                case "Status":
                    orders = orders.OrderBy(o => o.OrderStatus);
                    break;
                case "status_desc":
                    orders = orders.OrderByDescending(o => o.OrderStatus);
                    break;
                case "Total":
                    orders = orders.OrderBy(o => o.TotalAmount);
                    break;
                case "total_desc":
                    orders = orders.OrderByDescending(o => o.TotalAmount);
                    break;
                default:
                    orders = orders.OrderBy(o => o.OrderDate);
                    break;
            }

            ViewBag.StatusList = new SelectList(new[]
            {
                new { Value = "", Text = "Tất cả trạng thái" },
                new { Value = "Chờ xác nhận", Text = "Chờ xác nhận" },
                new { Value = "Đã xác nhận", Text = "Đã xác nhận" },
                new { Value = "Đang giao hàng", Text = "Đang giao hàng" },
                new { Value = "Đã giao hàng", Text = "Đã giao hàng" },
                new { Value = "Đã hủy", Text = "Đã hủy" }
            }, "Value", "Text", statusFilter);

            return View(await orders.AsNoTracking().ToListAsync());
        }

        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var medicationOrder = await _context.MedicationOrders
                .Include(m => m.Patient)
                .Include(m => m.Prescription)
                .ThenInclude(p => p.PrescriptionDetails)
                .ThenInclude(pd => pd.Medicine)
                .Include(m => m.Prescription.MedicalRecord)
                .ThenInclude(mr => mr.Doctor)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (medicationOrder == null)
            {
                return NotFound();
            }

            return View(medicationOrder);
        }

        public async Task<IActionResult> Create()
        {
            ViewData["PatientId"] = new SelectList(
                await _context.Patients
                    .Select(p => new { 
                        p.patient_id, 
                        DisplayText = $"{p.name} - {p.phone}" 
                    })
                    .ToListAsync(), 
                "patient_id", "DisplayText");

            ViewData["PrescriptionId"] = new SelectList(
                await _context.Prescriptions
                    .Include(p => p.MedicalRecord)
                    .ThenInclude(mr => mr.Patient)
                    .Where(p => p.Status == "Đã xử lý" && !p.MedicationOrders.Any())
                    .Select(p => new { 
                        p.Id, 
                        DisplayText = $"#{p.Id} - {p.MedicalRecord.Patient.name} - {p.TotalCost:C}" 
                    })
                    .ToListAsync(), 
                "Id", "DisplayText");

            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,PatientId,PrescriptionId,OrderDate,DeliveryAddress,ContactPhone,EstimatedDeliveryDate,Notes")] MedicationOrder medicationOrder)
        {
            if (ModelState.IsValid)
            {
                var existingOrder = await _context.MedicationOrders.FindAsync(medicationOrder.Id);
                if (existingOrder != null)
                {
                    ModelState.AddModelError("Id", "Mã đơn hàng đã tồn tại.");
                    await LoadCreateViewData();
                    return View(medicationOrder);
                }

                var prescription = await _context.Prescriptions.FindAsync(medicationOrder.PrescriptionId);
                if (prescription == null)
                {
                    ModelState.AddModelError("PrescriptionId", "Đơn thuốc không tồn tại.");
                    await LoadCreateViewData();
                    return View(medicationOrder);
                }

                medicationOrder.TotalAmount = prescription.TotalCost;
                medicationOrder.PaymentMethod = "COD";
                medicationOrder.OrderStatus = "Chờ xác nhận";
                medicationOrder.CreatedDate = DateTime.Now;
                medicationOrder.CreatedBy = User.Identity?.Name ?? "System";

                if (medicationOrder.EstimatedDeliveryDate == null)
                {
                    medicationOrder.EstimatedDeliveryDate = DateTime.Now.AddDays(3);
                }

                _context.Add(medicationOrder);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "Đơn hàng COD đã được tạo thành công.";
                return RedirectToAction(nameof(Index));
            }

            await LoadCreateViewData();
            return View(medicationOrder);
        }

        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var medicationOrder = await _context.MedicationOrders.FindAsync(id);
            if (medicationOrder == null)
            {
                return NotFound();
            }

            ViewData["OrderStatusList"] = new SelectList(new[]
            {
                new { Value = "Chờ xác nhận", Text = "Chờ xác nhận" },
                new { Value = "Đã xác nhận", Text = "Đã xác nhận" },
                new { Value = "Đang giao hàng", Text = "Đang giao hàng" },
                new { Value = "Đã giao hàng", Text = "Đã giao hàng" },
                new { Value = "Đã hủy", Text = "Đã hủy" }
            }, "Value", "Text", medicationOrder.OrderStatus);

            return View(medicationOrder);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,PatientId,PrescriptionId,OrderDate,TotalAmount,PaymentMethod,OrderStatus,DeliveryAddress,ContactPhone,EstimatedDeliveryDate,ActualDeliveryDate,Notes,CreatedDate,CreatedBy")] MedicationOrder medicationOrder)
        {
            if (id != medicationOrder.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    if (medicationOrder.OrderStatus == "Đã giao hàng" && medicationOrder.ActualDeliveryDate == null)
                    {
                        medicationOrder.ActualDeliveryDate = DateTime.Now;
                    }

                    _context.Update(medicationOrder);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "Đơn hàng đã được cập nhật thành công.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!MedicationOrderExists(medicationOrder.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }

            ViewData["OrderStatusList"] = new SelectList(new[]
            {
                new { Value = "Chờ xác nhận", Text = "Chờ xác nhận" },
                new { Value = "Đã xác nhận", Text = "Đã xác nhận" },
                new { Value = "Đang giao hàng", Text = "Đang giao hàng" },
                new { Value = "Đã giao hàng", Text = "Đã giao hàng" },
                new { Value = "Đã hủy", Text = "Đã hủy" }
            }, "Value", "Text", medicationOrder.OrderStatus);

            return View(medicationOrder);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateStatus(int id, string status)
        {
            var order = await _context.MedicationOrders.FindAsync(id);
            if (order != null)
            {
                order.OrderStatus = status;
                if (status == "Đã giao hàng" && order.ActualDeliveryDate == null)
                {
                    order.ActualDeliveryDate = DateTime.Now;
                }

                _context.Update(order);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = $"Trạng thái đơn hàng đã được cập nhật thành '{status}'.";
            }

            return RedirectToAction(nameof(Details), new { id = id });
        }

        public async Task<IActionResult> GetPrescriptionDetails(int prescriptionId)
        {
            var prescription = await _context.Prescriptions
                .Include(p => p.MedicalRecord)
                .ThenInclude(mr => mr.Patient)
                .Include(p => p.PrescriptionDetails)
                .ThenInclude(pd => pd.Medicine)
                .FirstOrDefaultAsync(p => p.Id == prescriptionId);

            if (prescription == null)
            {
                return NotFound();
            }

            return Json(new
            {
                patientId = prescription.MedicalRecord.patient_id,
                patientName = prescription.MedicalRecord.Patient.name,
                patientPhone = prescription.MedicalRecord.Patient.phone,
                patientAddress = prescription.MedicalRecord.Patient.address,
                totalAmount = prescription.TotalCost,
                details = prescription.PrescriptionDetails.Select(pd => new
                {
                    medicineName = pd.Medicine.Name,
                    quantity = pd.Quantity,
                    dosage = pd.Dosage,
                    unitPrice = pd.UnitPrice,
                    totalPrice = pd.TotalPrice
                })
            });
        }

        private async Task LoadCreateViewData()
        {
            ViewData["PatientId"] = new SelectList(
                await _context.Patients
                    .Select(p => new { 
                        p.patient_id, 
                        DisplayText = $"{p.name} - {p.phone}" 
                    })
                    .ToListAsync(), 
                "patient_id", "DisplayText");

            ViewData["PrescriptionId"] = new SelectList(
                await _context.Prescriptions
                    .Include(p => p.MedicalRecord)
                    .ThenInclude(mr => mr.Patient)
                    .Where(p => p.Status == "Đã xử lý" && !p.MedicationOrders.Any())
                    .Select(p => new { 
                        p.Id, 
                        DisplayText = $"#{p.Id} - {p.MedicalRecord.Patient.name} - {p.TotalCost:C}" 
                    })
                    .ToListAsync(), 
                "Id", "DisplayText");
        }

        private bool MedicationOrderExists(int id)
        {
            return _context.MedicationOrders.Any(e => e.Id == id);
        }
    }
}
