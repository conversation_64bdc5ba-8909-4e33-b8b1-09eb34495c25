using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Midterm1212.Models
{
    public class PrescriptionItem
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        [Required]
        public int prescription_item_id { get; set; }

        [Required]
        [Display(Name = "Đơn thuốc")]
        public int prescription_id { get; set; }

        [Required]
        [Display(Name = "Thuốc")]
        public int medication_id { get; set; }

        [Required]
        [Display(Name = "Số lượng")]
        public int quantity { get; set; }

        [Required]
        [Display(Name = "Hướng dẫn sử dụng")]
        public string dosage_instructions { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Tần suất")]
        public string frequency { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Thời gian sử dụng")]
        public string duration { get; set; } = string.Empty;

        [Required]
        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "Đơn giá")]
        public decimal unit_price { get; set; }

        [Required]
        [Column(TypeName = "decimal(12,2)")]
        [Display(Name = "Thành tiền")]
        public decimal total_price { get; set; }

        [Display(Name = "Ghi chú")]
        public string? notes { get; set; }

        [ForeignKey("prescription_id")]
        public virtual Prescription Prescription { get; set; }

        [ForeignKey("medication_id")]
        public virtual Medication Medication { get; set; }
    }
}
