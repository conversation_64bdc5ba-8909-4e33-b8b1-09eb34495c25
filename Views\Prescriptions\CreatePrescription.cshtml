@model Midterm1212.Models.Prescription

@{
    ViewData["Title"] = "Kê đơn thuốc";
    var medicalRecord = ViewBag.MedicalRecord as Midterm1212.Models.MedicalRecord;
    var medicines = ViewData["Medicines"] as IEnumerable<dynamic>;
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-prescription"></i> @ViewData["Title"]
                    </h4>
                </div>
                <div class="card-body">
                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            @TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <form asp-action="CreatePrescription" method="post" id="prescriptionForm">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <input type="hidden" asp-for="Id" />
                        <input type="hidden" asp-for="MedicalRecordId" />
                        <input type="hidden" asp-for="PrescriptionDate" />
                        <input type="hidden" asp-for="Status" />
                        <input type="hidden" asp-for="IsPaid" />
                        <input type="hidden" asp-for="CreatedDate" />

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Mã đơn thuốc</label>
                                    <input value="@Model.Id" class="form-control" readonly />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Ngày kê đơn</label>
                                    <input value="@Model.PrescriptionDate.ToString("dd/MM/yyyy HH:mm")" class="form-control" readonly />
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Instructions" class="form-label"></label>
                            <textarea asp-for="Instructions" class="form-control" rows="3" placeholder="Hướng dẫn sử dụng chung cho bệnh nhân..."></textarea>
                            <span asp-validation-for="Instructions" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Notes" class="form-label"></label>
                            <textarea asp-for="Notes" class="form-control" rows="2" placeholder="Ghi chú thêm..."></textarea>
                            <span asp-validation-for="Notes" class="text-danger"></span>
                        </div>

                        <hr>
                        <h5><i class="fas fa-pills"></i> Chi tiết đơn thuốc</h5>
                        
                        <div id="medicineDetails">
                            <div class="medicine-row border p-3 mb-3 rounded">
                                <div class="row">
                                    <div class="col-md-4">
                                        <label class="form-label">Thuốc</label>
                                        <select name="details[0].MedicineId" class="form-select medicine-select" onchange="updateMedicineInfo(this, 0)">
                                            <option value="">-- Chọn thuốc --</option>
                                            @if (medicines != null)
                                            {
                                                @foreach (var medicine in medicines)
                                                {
                                                    <option value="@medicine.Id" data-price="@medicine.Price" data-stock="@medicine.QuantityInStock" data-description="@medicine.Description">
                                                        @medicine.DisplayText
                                                    </option>
                                                }
                                            }
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">Số lượng</label>
                                        <input type="number" name="details[0].Quantity" class="form-control quantity-input" min="1" onchange="calculateTotal(0)" />
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Liều dùng</label>
                                        <input type="text" name="details[0].Dosage" class="form-control" placeholder="VD: 1 viên/lần" />
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Tần suất</label>
                                        <input type="text" name="details[0].Frequency" class="form-control" placeholder="VD: 3 lần/ngày" />
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-3">
                                        <label class="form-label">Thời gian</label>
                                        <input type="text" name="details[0].Duration" class="form-control" placeholder="VD: 7 ngày" />
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Hướng dẫn</label>
                                        <input type="text" name="details[0].Instructions" class="form-control" placeholder="Uống sau ăn..." />
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Thành tiền</label>
                                        <input type="text" class="form-control total-price" readonly />
                                        <button type="button" class="btn btn-danger btn-sm mt-1" onclick="removeMedicineRow(this)">
                                            <i class="fas fa-trash"></i> Xóa
                                        </button>
                                    </div>
                                </div>
                                <input type="hidden" name="details[0].Id" value="0" />
                            </div>
                        </div>

                        <div class="mb-3">
                            <button type="button" class="btn btn-success" onclick="addMedicineRow()">
                                <i class="fas fa-plus"></i> Thêm thuốc
                            </button>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle"></i> Lưu ý:</h6>
                                    <ul class="mb-0">
                                        <li>Kiểm tra kỹ liều lượng và tần suất sử dụng</li>
                                        <li>Đảm bảo đủ tồn kho trước khi kê đơn</li>
                                        <li>Hướng dẫn rõ ràng cho bệnh nhân</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6>Tổng cộng đơn thuốc:</h6>
                                        <h4 class="text-primary" id="grandTotal">0 ₫</h4>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between mt-3">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại danh sách
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Lưu đơn thuốc
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Thông tin hồ sơ bệnh án</h6>
                </div>
                <div class="card-body">
                    @if (medicalRecord != null)
                    {
                        <dl class="row">
                            <dt class="col-sm-5">Mã hồ sơ:</dt>
                            <dd class="col-sm-7">#@medicalRecord.record_id</dd>
                            
                            <dt class="col-sm-5">Bệnh nhân:</dt>
                            <dd class="col-sm-7"><strong>@medicalRecord.Patient.name</strong></dd>
                            
                            <dt class="col-sm-5">Bác sĩ:</dt>
                            <dd class="col-sm-7">BS. @medicalRecord.Doctor.name</dd>
                            
                            <dt class="col-sm-5">Ngày khám:</dt>
                            <dd class="col-sm-7">@medicalRecord.record_date.ToString("dd/MM/yyyy")</dd>
                            
                            <dt class="col-sm-5">Chẩn đoán:</dt>
                            <dd class="col-sm-7"><strong>@medicalRecord.diagnosis</strong></dd>
                            
                            @if (!string.IsNullOrEmpty(medicalRecord.treatment))
                            {
                                <dt class="col-sm-5">Điều trị:</dt>
                                <dd class="col-sm-7">@medicalRecord.treatment</dd>
                            }
                        </dl>
                    }
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">Thông tin bệnh nhân</h6>
                </div>
                <div class="card-body">
                    @if (medicalRecord?.Patient != null)
                    {
                        <dl class="row">
                            <dt class="col-sm-5">Tuổi:</dt>
                            <dd class="col-sm-7">@((DateTime.Now - medicalRecord.Patient.dob).Days / 365) tuổi</dd>
                            
                            <dt class="col-sm-5">Giới tính:</dt>
                            <dd class="col-sm-7">@medicalRecord.Patient.gender</dd>
                            
                            <dt class="col-sm-5">Điện thoại:</dt>
                            <dd class="col-sm-7">@medicalRecord.Patient.phone</dd>
                        </dl>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        let medicineRowIndex = 1;
        
        function addMedicineRow() {
            const container = document.getElementById('medicineDetails');
            const newRow = document.querySelector('.medicine-row').cloneNode(true);
            
            // Update indices
            newRow.querySelectorAll('select, input').forEach(element => {
                if (element.name) {
                    element.name = element.name.replace('[0]', `[${medicineRowIndex}]`);
                }
                if (element.type !== 'hidden') {
                    element.value = '';
                }
            });
            
            // Update onchange handlers
            const select = newRow.querySelector('.medicine-select');
            select.setAttribute('onchange', `updateMedicineInfo(this, ${medicineRowIndex})`);
            
            const quantityInput = newRow.querySelector('.quantity-input');
            quantityInput.setAttribute('onchange', `calculateTotal(${medicineRowIndex})`);
            
            container.appendChild(newRow);
            medicineRowIndex++;
        }
        
        function removeMedicineRow(button) {
            const rows = document.querySelectorAll('.medicine-row');
            if (rows.length > 1) {
                button.closest('.medicine-row').remove();
                calculateGrandTotal();
            }
        }
        
        function updateMedicineInfo(select, index) {
            const option = select.options[select.selectedIndex];
            const price = option.getAttribute('data-price');
            const stock = option.getAttribute('data-stock');
            
            const row = select.closest('.medicine-row');
            const quantityInput = row.querySelector('.quantity-input');
            
            if (stock) {
                quantityInput.max = stock;
                quantityInput.title = `Tồn kho: ${stock}`;
            }
            
            calculateTotal(index);
        }
        
        function calculateTotal(index) {
            const row = document.querySelectorAll('.medicine-row')[index];
            const select = row.querySelector('.medicine-select');
            const quantityInput = row.querySelector('.quantity-input');
            const totalPriceInput = row.querySelector('.total-price');
            
            const option = select.options[select.selectedIndex];
            const price = parseFloat(option.getAttribute('data-price')) || 0;
            const quantity = parseInt(quantityInput.value) || 0;
            
            const total = price * quantity;
            totalPriceInput.value = total.toLocaleString('vi-VN') + ' ₫';
            
            calculateGrandTotal();
        }
        
        function calculateGrandTotal() {
            let grandTotal = 0;
            document.querySelectorAll('.medicine-row').forEach((row, index) => {
                const select = row.querySelector('.medicine-select');
                const quantityInput = row.querySelector('.quantity-input');
                
                const option = select.options[select.selectedIndex];
                const price = parseFloat(option.getAttribute('data-price')) || 0;
                const quantity = parseInt(quantityInput.value) || 0;
                
                grandTotal += price * quantity;
            });
            
            document.getElementById('grandTotal').textContent = grandTotal.toLocaleString('vi-VN') + ' ₫';
        }
        
        // Auto-hide alerts
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    </script>
}
