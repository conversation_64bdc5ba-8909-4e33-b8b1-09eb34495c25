@model Midterm1212.Models.ViewModels.AdminDashboardViewModel
@{
    ViewData["Title"] = "Trang quản trị";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-danger">
                    <i class="fas fa-user-shield"></i> Bảng điều khiển quản trị
                </h2>
                <div>
                    <a asp-controller="Account" asp-action="ManageUsers" class="btn btn-outline-primary">
                        <i class="fas fa-users-cog"></i> Quản lý người dùng
                    </a>
                    <a asp-controller="Account" asp-action="ChangePassword" class="btn btn-outline-warning">
                        <i class="fas fa-key"></i> <PERSON><PERSON><PERSON> m<PERSON> kh<PERSON>
                    </a>
                </div>
            </div>

            <!-- Main Statistics -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <h4>@Model.TotalPatients</h4>
                            <p class="mb-0">Bệnh nhân</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-user-md fa-2x mb-2"></i>
                            <h4>@Model.TotalDoctors</h4>
                            <p class="mb-0">Bác sĩ</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-calendar-alt fa-2x mb-2"></i>
                            <h4>@Model.TotalAppointments</h4>
                            <p class="mb-0">Lịch hẹn</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-warning text-dark">
                        <div class="card-body text-center">
                            <i class="fas fa-pills fa-2x mb-2"></i>
                            <h4>@Model.TotalMedicines</h4>
                            <p class="mb-0">Loại thuốc</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-secondary text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-prescription fa-2x mb-2"></i>
                            <h4>@Model.TotalPrescriptions</h4>
                            <p class="mb-0">Đơn thuốc</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-dark text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-truck fa-2x mb-2"></i>
                            <h4>@Model.TotalMedicationOrders</h4>
                            <p class="mb-0">Đơn COD</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alerts and Warnings -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-warning">
                        <div class="card-header bg-warning">
                            <h6 class="mb-0">
                                <i class="fas fa-exclamation-triangle"></i> Cảnh báo tồn kho
                            </h6>
                        </div>
                        <div class="card-body">
                            <h4 class="text-warning">@Model.LowStockMedicines</h4>
                            <p class="mb-2">Thuốc tồn kho thấp</p>
                            <a asp-controller="Medicines" asp-action="LowStock" class="btn btn-warning btn-sm">
                                Xem chi tiết
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-calendar-times"></i> Thuốc hết hạn
                            </h6>
                        </div>
                        <div class="card-body">
                            <h4 class="text-danger">@Model.ExpiredMedicines</h4>
                            <p class="mb-2">Thuốc đã hết hạn</p>
                            <a asp-controller="Medicines" asp-action="Expired" class="btn btn-danger btn-sm">
                                Xem chi tiết
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-info">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-clock"></i> Lịch hẹn chờ
                            </h6>
                        </div>
                        <div class="card-body">
                            <h4 class="text-info">@Model.PendingAppointments</h4>
                            <p class="mb-2">Lịch hẹn chờ xác nhận</p>
                            <a asp-controller="Appointments" asp-action="Pending" class="btn btn-info btn-sm">
                                Xem chi tiết
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-money-bill-wave"></i> Doanh thu tháng
                            </h6>
                        </div>
                        <div class="card-body">
                            <h4 class="text-success">@Model.MonthlyRevenue.ToString("C")</h4>
                            <p class="mb-2">Doanh thu COD</p>
                            <a asp-controller="MedicationOrders" asp-action="Index" class="btn btn-success btn-sm">
                                Xem chi tiết
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Recent Appointments -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-calendar-alt"></i> Lịch hẹn gần đây
                            </h5>
                        </div>
                        <div class="card-body">
                            @if (Model.RecentAppointments != null && Model.RecentAppointments.Count > 0)
                            {
                                @foreach (var appointment in Model.RecentAppointments)
                                {
                                    <div class="border-bottom pb-2 mb-2">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <strong>@appointment.Patient.name</strong><br>
                                                <small class="text-muted">BS. @appointment.Doctor.name</small>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge @(appointment.status == "Pending" ? "bg-warning" : appointment.status == "Confirmed" ? "bg-info" : "bg-success")">
                                                    @appointment.status
                                                </span>
                                            </div>
                                        </div>
                                        <div class="mt-1">
                                            <i class="fas fa-calendar"></i> @appointment.appointment_date.ToString("dd/MM/yyyy")
                                            <i class="fas fa-clock ms-2"></i> @appointment.appointment_time.ToString(@"hh\:mm")
                                        </div>
                                    </div>
                                }
                                <div class="text-center mt-3">
                                    <a asp-controller="Appointments" asp-action="Index" class="btn btn-outline-primary btn-sm">
                                        Xem tất cả lịch hẹn
                                    </a>
                                </div>
                            }
                            else
                            {
                                <div class="text-center text-muted">
                                    <i class="fas fa-calendar-times fa-3x mb-3"></i>
                                    <p>Chưa có lịch hẹn nào</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <!-- Recent Orders -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-truck"></i> Đơn hàng COD gần đây
                            </h5>
                        </div>
                        <div class="card-body">
                            @if (Model.RecentOrders != null && Model.RecentOrders.Count > 0)
                            {
                                @foreach (var order in Model.RecentOrders)
                                {
                                    <div class="border-bottom pb-2 mb-2">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <strong>Đơn #@order.Id</strong><br>
                                                <small class="text-muted">@order.Patient.name</small>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge @(order.OrderStatus == "Chờ xác nhận" ? "bg-warning" : order.OrderStatus == "Đã giao hàng" ? "bg-success" : "bg-info")">
                                                    @order.OrderStatus
                                                </span>
                                            </div>
                                        </div>
                                        <div class="mt-1">
                                            <i class="fas fa-money-bill"></i> @order.TotalAmount.ToString("C")
                                            <i class="fas fa-calendar ms-2"></i> @order.OrderDate.ToString("dd/MM/yyyy")
                                        </div>
                                    </div>
                                }
                                <div class="text-center mt-3">
                                    <a asp-controller="MedicationOrders" asp-action="Index" class="btn btn-outline-success btn-sm">
                                        Xem tất cả đơn hàng
                                    </a>
                                </div>
                            }
                            else
                            {
                                <div class="text-center text-muted">
                                    <i class="fas fa-truck fa-3x mb-3"></i>
                                    <p>Chưa có đơn hàng nào</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-bolt"></i> Thao tác nhanh
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-2">
                                    <a asp-controller="Account" asp-action="CreateDoctorAccount" class="btn btn-primary w-100 mb-2">
                                        <i class="fas fa-user-plus"></i><br>
                                        Tạo tài khoản bác sĩ
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <a asp-controller="Medicines" asp-action="Index" class="btn btn-success w-100 mb-2">
                                        <i class="fas fa-pills"></i><br>
                                        Quản lý thuốc
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <a asp-controller="Prescriptions" asp-action="Index" class="btn btn-info w-100 mb-2">
                                        <i class="fas fa-prescription"></i><br>
                                        Quản lý đơn thuốc
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <a asp-controller="MedicationOrders" asp-action="Index" class="btn btn-warning w-100 mb-2">
                                        <i class="fas fa-truck"></i><br>
                                        Quản lý COD
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <a asp-controller="Patients" asp-action="Index" class="btn btn-secondary w-100 mb-2">
                                        <i class="fas fa-users"></i><br>
                                        Quản lý bệnh nhân
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <a asp-controller="Doctor" asp-action="Index" class="btn btn-dark w-100 mb-2">
                                        <i class="fas fa-user-md"></i><br>
                                        Quản lý bác sĩ
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
