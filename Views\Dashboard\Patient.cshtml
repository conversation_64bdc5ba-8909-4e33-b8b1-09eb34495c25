@{
    ViewData["Title"] = "Trang chủ bệnh nhân";
    var dashboardData = ViewBag.DashboardData;
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-user"></i> Ch<PERSON><PERSON> mừng, @dashboardData.Patient.name
                </h2>
                <div>
                    <a asp-controller="My" asp-action="Profile" class="btn btn-outline-primary">
                        <i class="fas fa-user-edit"></i> <PERSON><PERSON> sơ cá nhân
                    </a>
                    <a asp-controller="Account" asp-action="ChangePassword" class="btn btn-outline-warning">
                        <i class="fas fa-key"></i> Đổi mật khẩu
                    </a>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>@dashboardData.TotalAppointments</h4>
                                    <p class="mb-0">Tổng lịch hẹn</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calendar-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>@dashboardData.TotalMedicalRecords</h4>
                                    <p class="mb-0">Hồ sơ bệnh án</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-file-medical fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-dark">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>@dashboardData.PendingPrescriptions</h4>
                                    <p class="mb-0">Đơn thuốc chờ đặt</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-prescription fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>@dashboardData.UpcomingAppointments.Count()</h4>
                                    <p class="mb-0">Lịch hẹn sắp tới</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Upcoming Appointments -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-calendar-check"></i> Lịch hẹn sắp tới
                            </h5>
                        </div>
                        <div class="card-body">
                            @if (dashboardData.UpcomingAppointments.Any())
                            {
                                @foreach (var appointment in dashboardData.UpcomingAppointments)
                                {
                                    <div class="border-bottom pb-2 mb-2">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <strong>BS. @appointment.Doctor.name</strong><br>
                                                <small class="text-muted">@appointment.Doctor.specialization</small>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge bg-info">@appointment.status</span>
                                            </div>
                                        </div>
                                        <div class="mt-1">
                                            <i class="fas fa-calendar"></i> @appointment.appointment_date.ToString("dd/MM/yyyy")
                                            <i class="fas fa-clock ms-2"></i> @appointment.appointment_time.ToString(@"hh\:mm")
                                        </div>
                                    </div>
                                }
                                <div class="text-center mt-3">
                                    <a asp-controller="My" asp-action="Appointments" class="btn btn-outline-primary btn-sm">
                                        Xem tất cả lịch hẹn
                                    </a>
                                </div>
                            }
                            else
                            {
                                <div class="text-center text-muted">
                                    <i class="fas fa-calendar-times fa-3x mb-3"></i>
                                    <p>Không có lịch hẹn sắp tới</p>
                                    <a asp-controller="My" asp-action="BookAppointment" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> Đặt lịch hẹn
                                    </a>
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <!-- Recent Medical Records -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-file-medical-alt"></i> Hồ sơ bệnh án gần đây
                            </h5>
                        </div>
                        <div class="card-body">
                            @if (dashboardData.RecentMedicalRecords.Any())
                            {
                                @foreach (var record in dashboardData.RecentMedicalRecords)
                                {
                                    <div class="border-bottom pb-2 mb-2">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <strong>@record.diagnosis</strong><br>
                                                <small class="text-muted">BS. @record.Doctor.name</small>
                                            </div>
                                            <div class="text-end">
                                                <small>@record.record_date.ToString("dd/MM/yyyy")</small>
                                                @if (record.Prescriptions.Any())
                                                {
                                                    <br><span class="badge bg-success">Có đơn thuốc</span>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                }
                                <div class="text-center mt-3">
                                    <a asp-controller="My" asp-action="MedicalRecords" class="btn btn-outline-success btn-sm">
                                        Xem tất cả hồ sơ
                                    </a>
                                </div>
                            }
                            else
                            {
                                <div class="text-center text-muted">
                                    <i class="fas fa-file-medical fa-3x mb-3"></i>
                                    <p>Chưa có hồ sơ bệnh án</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-bolt"></i> Thao tác nhanh
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <a asp-controller="My" asp-action="BookAppointment" class="btn btn-primary w-100 mb-2">
                                        <i class="fas fa-calendar-plus"></i><br>
                                        Đặt lịch hẹn
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a asp-controller="Doctor" asp-action="Index" class="btn btn-info w-100 mb-2">
                                        <i class="fas fa-user-md"></i><br>
                                        Xem bác sĩ
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a asp-controller="My" asp-action="Prescriptions" class="btn btn-success w-100 mb-2">
                                        <i class="fas fa-prescription"></i><br>
                                        Đơn thuốc của tôi
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a asp-controller="My" asp-action="Profile" class="btn btn-warning w-100 mb-2">
                                        <i class="fas fa-user-edit"></i><br>
                                        Cập nhật hồ sơ
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
