﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Midterm1212</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/Midterm1212.styles.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
            <div class="container-fluid">
                <div class="navbar-nav">
                    <a class="nav-link" href="@Url.Action("Index", "Dashboard")">
                        <i class="fas fa-home"></i> Trang chủ
                    </a>

                    @if (User.IsInRole("Patient"))
                    {
                        <a class="nav-link" href="@Url.Action("Index", "Doctor")">
                            <i class="fas fa-user-md"></i> Bác sĩ
                        </a>
                        <a class="nav-link" href="@Url.Action("BookAppointment", "My")">
                            <i class="fas fa-calendar-plus"></i> Đặt lịch hẹn
                        </a>
                        <a class="nav-link" href="@Url.Action("Appointments", "My")">
                            <i class="fas fa-calendar-alt"></i> Lịch hẹn của tôi
                        </a>
                        <a class="nav-link" href="@Url.Action("Prescriptions", "My")">
                            <i class="fas fa-prescription"></i> Đơn thuốc của tôi
                        </a>
                    }

                    @if (User.IsInRole("Doctor"))
                    {
                        <a class="nav-link" href="@Url.Action("Index", "Appointments")">
                            <i class="fas fa-calendar-alt"></i> Lịch hẹn
                        </a>
                        <a class="nav-link" href="@Url.Action("Index", "Patients")">
                            <i class="fas fa-users"></i> Bệnh nhân
                        </a>
                        <a class="nav-link" href="@Url.Action("Index", "Prescriptions")">
                            <i class="fas fa-prescription"></i> Đơn thuốc
                        </a>
                    }

                    @if (User.IsInRole("Admin"))
                    {
                        <a class="nav-link" href="@Url.Action("Index", "Doctor")">
                            <i class="fas fa-user-md"></i> Bác sĩ
                        </a>
                        <a class="nav-link" href="@Url.Action("Index", "Patients")">
                            <i class="fas fa-users"></i> Bệnh nhân
                        </a>
                        <a class="nav-link" href="@Url.Action("Index", "Appointments")">
                            <i class="fas fa-calendar-alt"></i> Lịch hẹn
                        </a>
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-pills"></i> Quản lý thuốc
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="@Url.Action("Index", "Medicines")">
                                    <i class="fas fa-capsules"></i> Danh sách thuốc
                                </a></li>
                                <li><a class="dropdown-item" href="@Url.Action("Index", "Prescriptions")">
                                    <i class="fas fa-prescription"></i> Đơn thuốc
                                </a></li>
                                <li><a class="dropdown-item" href="@Url.Action("Index", "MedicationOrders")">
                                    <i class="fas fa-truck"></i> Đơn hàng COD
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="@Url.Action("LowStock", "Medicines")">
                                    <i class="fas fa-exclamation-triangle text-warning"></i> Tồn kho thấp
                                </a></li>
                                <li><a class="dropdown-item" href="@Url.Action("Expired", "Medicines")">
                                    <i class="fas fa-calendar-times text-danger"></i> Thuốc hết hạn
                                </a></li>
                            </ul>
                        </div>
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-cog"></i> Quản trị
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="@Url.Action("ManageUsers", "Account")">
                                    <i class="fas fa-users-cog"></i> Quản lý người dùng
                                </a></li>
                                <li><a class="dropdown-item" href="@Url.Action("CreateDoctorAccount", "Account")">
                                    <i class="fas fa-user-plus"></i> Tạo tài khoản bác sĩ
                                </a></li>
                            </ul>
                        </div>
                    }
                </div>
                <div class="navbar-nav ms-auto">
                    @Html.Partial("_LoginPartial")
                </div>
            </div>
        </nav>
    </header>

    <div class="container-fluid">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="text-muted mt-4">
        <div class="container-fluid">
            © 2025 - Student's ID - Your Name
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
