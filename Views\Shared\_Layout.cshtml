﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Midterm1212</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/Midterm1212.styles.css" asp-append-version="true" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
            <div class="container-fluid">
                <div class="navbar-nav">
                    <a class="nav-link" href="@Url.Action("Index", "Doctor")"><PERSON><PERSON><PERSON> s<PERSON></a>
                    <a class="nav-link" href="@Url.Action("Index", "Patients")">Bệnh nhân</a>
                    <a class="nav-link" href="@Url.Action("Index", "Appointments")">Lịch hẹn</a>
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="pharmacyDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            Nhà thuốc
                        </a>
                        <div class="dropdown-menu" aria-labelledby="pharmacyDropdown">
                            <a class="dropdown-item" href="@Url.Action("Index", "Medications")">Quản lý thuốc</a>
                            <a class="dropdown-item" href="@Url.Action("Index", "MedicationCategories")">Danh mục thuốc</a>
                            <a class="dropdown-item" href="@Url.Action("Index", "Prescriptions")">Đơn thuốc</a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="@Url.Action("LowStock", "Medications")">Tồn kho thấp</a>
                            <a class="dropdown-item" href="@Url.Action("ExpiringSoon", "Medications")">Sắp hết hạn</a>
                        </div>
                    </div>
                </div>
                <div class="navbar-nav ms-auto">
                    @Html.Partial("_LoginPartial")
                </div>
            </div>
        </nav>
    </header>

    <div class="container-fluid">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="text-muted mt-4">
        <div class="container-fluid">
            © 2025 - Student's ID - Your Name
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
