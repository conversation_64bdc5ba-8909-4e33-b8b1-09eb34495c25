using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Midterm1212.Models
{
    public class MedicationCategory
    {
        public MedicationCategory()
        {
            Medications = new HashSet<Medication>();
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        [Required]
        public int category_id { get; set; }

        [Required]
        [Display(Name = "Tên danh mục")]
        public string category_name { get; set; } = string.Empty;

        [Display(Name = "Mô tả")]
        public string? description { get; set; }

        [Required]
        [Display(Name = "Ng<PERSON>y tạo")]
        public DateTime created_date { get; set; } = DateTime.Now;

        public virtual ICollection<Medication> Medications { get; set; }
    }
}
