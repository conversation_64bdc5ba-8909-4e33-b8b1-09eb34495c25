@model Midterm1212.Models.MedicationOrder

@{
    ViewData["Title"] = "Đặt thuốc COD";
    var prescription = ViewBag.Prescription as Midterm1212.Models.Prescription;
}

<div class="container">
    <div class="row">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-truck"></i> @ViewData["Title"]
                    </h4>
                </div>
                <div class="card-body">
                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            @TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <form asp-action="CreateFromPrescription" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <input type="hidden" asp-for="Id" />
                        <input type="hidden" asp-for="PatientId" />
                        <input type="hidden" asp-for="PrescriptionId" />
                        <input type="hidden" asp-for="OrderDate" />
                        <input type="hidden" asp-for="TotalAmount" />
                        <input type="hidden" asp-for="PaymentMethod" />
                        <input type="hidden" asp-for="OrderStatus" />
                        <input type="hidden" asp-for="CreatedDate" />
                        <input type="hidden" asp-for="CreatedBy" />

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Mã đơn hàng</label>
                                    <input value="@Model.Id" class="form-control" readonly />
                                    <div class="form-text">Mã đơn hàng được tạo tự động</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Ngày đặt hàng</label>
                                    <input value="@Model.OrderDate.ToString("dd/MM/yyyy HH:mm")" class="form-control" readonly />
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Phương thức thanh toán</label>
                                    <input value="COD (Thanh toán khi nhận hàng)" class="form-control" readonly />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Tổng tiền</label>
                                    <input value="@Model.TotalAmount.ToString("C")" class="form-control text-primary fw-bold" readonly />
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="DeliveryAddress" class="form-label"></label>
                            <textarea asp-for="DeliveryAddress" class="form-control" rows="3" placeholder="Nhập địa chỉ giao hàng chi tiết..."></textarea>
                            <span asp-validation-for="DeliveryAddress" class="text-danger"></span>
                            <div class="form-text">Vui lòng cung cấp địa chỉ chính xác để giao hàng</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="ContactPhone" class="form-label"></label>
                                    <input asp-for="ContactPhone" class="form-control" placeholder="Số điện thoại liên hệ" />
                                    <span asp-validation-for="ContactPhone" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="EstimatedDeliveryDate" class="form-label"></label>
                                    <input asp-for="EstimatedDeliveryDate" class="form-control" type="date" />
                                    <span asp-validation-for="EstimatedDeliveryDate" class="text-danger"></span>
                                    <div class="form-text">Dự kiến giao hàng trong 2-3 ngày</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Notes" class="form-label"></label>
                            <textarea asp-for="Notes" class="form-control" rows="2" placeholder="Ghi chú thêm cho đơn hàng (nếu có)..."></textarea>
                            <span asp-validation-for="Notes" class="text-danger"></span>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Thông tin giao hàng COD:</h6>
                            <ul class="mb-0">
                                <li><strong>Miễn phí giao hàng</strong> trong nội thành</li>
                                <li><strong>Thanh toán khi nhận hàng</strong> - không cần trả trước</li>
                                <li><strong>Thời gian giao hàng:</strong> 2-3 ngày làm việc</li>
                                <li><strong>Kiểm tra hàng</strong> trước khi thanh toán</li>
                                <li><strong>Hotline hỗ trợ:</strong> 1900-xxxx</li>
                            </ul>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-controller="Prescriptions" asp-action="ViewPrescriptionResult" asp-route-prescriptionId="@Model.PrescriptionId" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại đơn thuốc
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-truck"></i> Xác nhận đặt hàng COD
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Thông tin đơn thuốc</h6>
                </div>
                <div class="card-body">
                    @if (prescription != null)
                    {
                        <dl class="row">
                            <dt class="col-sm-5">Mã đơn thuốc:</dt>
                            <dd class="col-sm-7">#@prescription.Id</dd>
                            
                            <dt class="col-sm-5">Bệnh nhân:</dt>
                            <dd class="col-sm-7"><strong>@prescription.MedicalRecord.Patient.name</strong></dd>
                            
                            <dt class="col-sm-5">Bác sĩ:</dt>
                            <dd class="col-sm-7">BS. @prescription.MedicalRecord.Doctor.name</dd>
                            
                            <dt class="col-sm-5">Ngày kê đơn:</dt>
                            <dd class="col-sm-7">@prescription.PrescriptionDate.ToString("dd/MM/yyyy")</dd>
                        </dl>
                    }
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">Chi tiết thuốc</h6>
                </div>
                <div class="card-body">
                    @if (prescription?.PrescriptionDetails != null && prescription.PrescriptionDetails.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Thuốc</th>
                                        <th>SL</th>
                                        <th>Giá</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var detail in prescription.PrescriptionDetails)
                                    {
                                        <tr>
                                            <td>
                                                <strong>@detail.Medicine.Name</strong>
                                                @if (!string.IsNullOrEmpty(detail.Dosage))
                                                {
                                                    <br><small class="text-muted">@detail.Dosage</small>
                                                }
                                            </td>
                                            <td>@detail.Quantity</td>
                                            <td><strong>@detail.TotalPrice.ToString("C")</strong></td>
                                        </tr>
                                    }
                                </tbody>
                                <tfoot>
                                    <tr class="table-success">
                                        <th colspan="2">Tổng cộng:</th>
                                        <th>@prescription.TotalCost.ToString("C")</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    }
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">Hướng dẫn sử dụng</h6>
                </div>
                <div class="card-body">
                    @if (!string.IsNullOrEmpty(prescription?.Instructions))
                    {
                        <p class="mb-0">@prescription.Instructions</p>
                    }
                    else
                    {
                        <p class="text-muted mb-0">Không có hướng dẫn đặc biệt</p>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Set minimum delivery date to tomorrow
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        document.getElementById('EstimatedDeliveryDate').min = tomorrow.toISOString().split('T')[0];
        
        // Set max delivery date to 7 days from now
        const maxDate = new Date();
        maxDate.setDate(maxDate.getDate() + 7);
        document.getElementById('EstimatedDeliveryDate').max = maxDate.toISOString().split('T')[0];
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    </script>
}
