using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Midterm1212.Models;

namespace Midterm1212.Controllers
{
    [Authorize(Roles = "Patient")]
    public class MyController : Controller
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly MedicalDbContext _context;

        public MyController(UserManager<ApplicationUser> userManager, MedicalDbContext context)
        {
            _userManager = userManager;
            _context = context;
        }

        private async Task<int?> GetCurrentPatientId()
        {
            var user = await _userManager.GetUserAsync(User);
            return user?.PatientId;
        }

        public async Task<IActionResult> Profile()
        {
            var patientId = await GetCurrentPatientId();
            if (patientId == null)
            {
                return RedirectToAction("Login", "Account");
            }

            var patient = await _context.Patients.FindAsync(patientId);
            if (patient == null)
            {
                return NotFound();
            }

            return View(patient);
        }

        [HttpGet]
        public async Task<IActionResult> EditProfile()
        {
            var patientId = await GetCurrentPatientId();
            if (patientId == null)
            {
                return RedirectToAction("Login", "Account");
            }

            var patient = await _context.Patients.FindAsync(patientId);
            if (patient == null)
            {
                return NotFound();
            }

            return View(patient);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditProfile(Patient patient)
        {
            var patientId = await GetCurrentPatientId();
            if (patientId == null || patient.patient_id != patientId)
            {
                return RedirectToAction("Login", "Account");
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(patient);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "Cập nhật thông tin cá nhân thành công!";
                    return RedirectToAction(nameof(Profile));
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!PatientExists(patient.patient_id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
            }
            return View(patient);
        }

        public async Task<IActionResult> Appointments(string sortOrder, string statusFilter)
        {
            var patientId = await GetCurrentPatientId();
            if (patientId == null)
            {
                return RedirectToAction("Login", "Account");
            }

            ViewData["DateSortParm"] = String.IsNullOrEmpty(sortOrder) ? "date_desc" : "";
            ViewData["StatusSortParm"] = sortOrder == "Status" ? "status_desc" : "Status";
            ViewData["StatusFilter"] = statusFilter;

            var appointments = from a in _context.Appointments
                              .Include(a => a.Doctor)
                              .Where(a => a.patient_id == patientId)
                              select a;

            if (!String.IsNullOrEmpty(statusFilter))
            {
                appointments = appointments.Where(a => a.status == statusFilter);
            }

            switch (sortOrder)
            {
                case "date_desc":
                    appointments = appointments.OrderByDescending(a => a.appointment_date).ThenByDescending(a => a.appointment_time);
                    break;
                case "Status":
                    appointments = appointments.OrderBy(a => a.status);
                    break;
                case "status_desc":
                    appointments = appointments.OrderByDescending(a => a.status);
                    break;
                default:
                    appointments = appointments.OrderBy(a => a.appointment_date).ThenBy(a => a.appointment_time);
                    break;
            }

            ViewBag.StatusList = new SelectList(new[]
            {
                new { Value = "", Text = "Tất cả trạng thái" },
                new { Value = "Pending", Text = "Chờ xác nhận" },
                new { Value = "Confirmed", Text = "Đã xác nhận" },
                new { Value = "Complete", Text = "Hoàn thành" },
                new { Value = "Cancelled", Text = "Đã hủy" }
            }, "Value", "Text", statusFilter);

            return View(await appointments.AsNoTracking().ToListAsync());
        }

        [HttpGet]
        public async Task<IActionResult> BookAppointment()
        {
            var patientId = await GetCurrentPatientId();
            if (patientId == null)
            {
                return RedirectToAction("Login", "Account");
            }

            ViewData["doctor_id"] = new SelectList(
                await _context.Doctors.Select(d => new { 
                    d.doctor_id, 
                    DisplayText = $"{d.name} - {d.specialization}" 
                }).ToListAsync(), 
                "doctor_id", "DisplayText");

            var maxAppointmentId = await _context.Appointments.AnyAsync() 
                ? await _context.Appointments.MaxAsync(a => a.appointment_id) 
                : 0;

            var appointment = new Appointment
            {
                appointment_id = maxAppointmentId + 1,
                patient_id = patientId.Value,
                appointment_date = DateTime.Today.AddDays(1),
                status = "Pending"
            };

            return View(appointment);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> BookAppointment([Bind("appointment_id,doctor_id,appointment_date,appointment_time,status")] Appointment appointment)
        {
            var patientId = await GetCurrentPatientId();
            if (patientId == null)
            {
                return RedirectToAction("Login", "Account");
            }

            appointment.patient_id = patientId.Value;

            if (ModelState.IsValid)
            {
                var existingAppointment = await _context.Appointments.FindAsync(appointment.appointment_id);
                if (existingAppointment != null)
                {
                    ModelState.AddModelError("appointment_id", "Mã lịch hẹn đã tồn tại.");
                    await LoadDoctorDropdown();
                    return View(appointment);
                }

                // Check for conflicting appointments
                var conflictingAppointment = await _context.Appointments
                    .AnyAsync(a => a.doctor_id == appointment.doctor_id &&
                                  a.appointment_date.Date == appointment.appointment_date.Date &&
                                  a.appointment_time == appointment.appointment_time &&
                                  a.status != "Cancelled");

                if (conflictingAppointment)
                {
                    ModelState.AddModelError("appointment_time", "Bác sĩ đã có lịch hẹn vào thời gian này.");
                    await LoadDoctorDropdown();
                    return View(appointment);
                }

                _context.Add(appointment);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "Đặt lịch hẹn thành công!";
                return RedirectToAction(nameof(Appointments));
            }

            await LoadDoctorDropdown();
            return View(appointment);
        }

        public async Task<IActionResult> MedicalRecords()
        {
            var patientId = await GetCurrentPatientId();
            if (patientId == null)
            {
                return RedirectToAction("Login", "Account");
            }

            var medicalRecords = await _context.MedicalRecords
                .Include(mr => mr.Doctor)
                .Include(mr => mr.Appointment)
                .Include(mr => mr.Prescriptions)
                .Where(mr => mr.patient_id == patientId)
                .OrderByDescending(mr => mr.record_date)
                .ToListAsync();

            return View(medicalRecords);
        }

        public async Task<IActionResult> Prescriptions()
        {
            var patientId = await GetCurrentPatientId();
            if (patientId == null)
            {
                return RedirectToAction("Login", "Account");
            }

            var prescriptions = await _context.Prescriptions
                .Include(p => p.MedicalRecord)
                .ThenInclude(mr => mr.Doctor)
                .Include(p => p.PrescriptionDetails)
                .ThenInclude(pd => pd.Medicine)
                .Include(p => p.MedicationOrders)
                .Where(p => p.MedicalRecord.patient_id == patientId)
                .OrderByDescending(p => p.PrescriptionDate)
                .ToListAsync();

            return View(prescriptions);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> OrderMedication(int prescriptionId)
        {
            var patientId = await GetCurrentPatientId();
            if (patientId == null)
            {
                return RedirectToAction("Login", "Account");
            }

            var prescription = await _context.Prescriptions
                .Include(p => p.MedicalRecord)
                .Include(p => p.MedicationOrders)
                .FirstOrDefaultAsync(p => p.Id == prescriptionId && p.MedicalRecord.patient_id == patientId);

            if (prescription == null)
            {
                return NotFound();
            }

            if (prescription.Status != "Đã xử lý")
            {
                TempData["ErrorMessage"] = "Chỉ có thể đặt hàng cho đơn thuốc đã được xử lý.";
                return RedirectToAction(nameof(Prescriptions));
            }

            if (prescription.MedicationOrders.Any())
            {
                TempData["ErrorMessage"] = "Đơn thuốc này đã có đơn hàng.";
                return RedirectToAction(nameof(Prescriptions));
            }

            return RedirectToAction("Create", "MedicationOrders", new { prescriptionId = prescriptionId });
        }

        private async Task LoadDoctorDropdown()
        {
            ViewData["doctor_id"] = new SelectList(
                await _context.Doctors.Select(d => new { 
                    d.doctor_id, 
                    DisplayText = $"{d.name} - {d.specialization}" 
                }).ToListAsync(), 
                "doctor_id", "DisplayText");
        }

        private bool PatientExists(int id)
        {
            return _context.Patients.Any(e => e.patient_id == id);
        }
    }
}
