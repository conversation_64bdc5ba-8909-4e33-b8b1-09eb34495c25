@model IEnumerable<Midterm1212.Models.Medicine>

@{
    ViewData["Title"] = "Quản lý thuốc";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h2>@ViewData["Title"]</h2>
                <a asp-action="Create" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Thêm thuốc mới
                </a>
            </div>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="card border-warning">
                        <div class="card-body">
                            <h5 class="card-title text-warning">
                                <i class="fas fa-exclamation-triangle"></i> Cảnh báo tồn kho thấp
                            </h5>
                            <p class="card-text">
                                <strong>@ViewBag.LowStockCount</strong> loại thuốc có tồn kho thấp
                            </p>
                            <a asp-action="LowStock" class="btn btn-warning btn-sm">Xem chi tiết</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-danger">
                        <div class="card-body">
                            <h5 class="card-title text-danger">
                                <i class="fas fa-calendar-times"></i> Thuốc hết hạn
                            </h5>
                            <p class="card-text">
                                <strong>@ViewBag.ExpiredCount</strong> loại thuốc đã hết hạn
                            </p>
                            <a asp-action="Expired" class="btn btn-danger btn-sm">Xem chi tiết</a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <form asp-action="Index" method="get" class="row g-3">
                        <div class="col-md-4">
                            <input type="text" name="SearchString" value="@ViewData["CurrentFilter"]" 
                                   class="form-control" placeholder="Tìm kiếm theo tên, mô tả, nhà sản xuất..." />
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search"></i> Tìm kiếm
                            </button>
                        </div>
                        <div class="col-md-2">
                            <a asp-action="Index" class="btn btn-outline-secondary">
                                <i class="fas fa-redo"></i> Làm mới
                            </a>
                        </div>
                    </form>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>
                                        <a asp-action="Index" asp-route-sortOrder="@ViewData["NameSortParm"]" 
                                           asp-route-searchString="@ViewData["CurrentFilter"]">
                                            Tên thuốc <i class="fas fa-sort"></i>
                                        </a>
                                    </th>
                                    <th>Mô tả</th>
                                    <th>
                                        <a asp-action="Index" asp-route-sortOrder="@ViewData["StockSortParm"]" 
                                           asp-route-searchString="@ViewData["CurrentFilter"]">
                                            Tồn kho <i class="fas fa-sort"></i>
                                        </a>
                                    </th>
                                    <th>
                                        <a asp-action="Index" asp-route-sortOrder="@ViewData["PriceSortParm"]" 
                                           asp-route-searchString="@ViewData["CurrentFilter"]">
                                            Giá <i class="fas fa-sort"></i>
                                        </a>
                                    </th>
                                    <th>
                                        <a asp-action="Index" asp-route-sortOrder="@ViewData["ExpiryDateSortParm"]" 
                                           asp-route-searchString="@ViewData["CurrentFilter"]">
                                            Ngày hết hạn <i class="fas fa-sort"></i>
                                        </a>
                                    </th>
                                    <th>Trạng thái</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>
                                            <strong>@Html.DisplayFor(modelItem => item.Name)</strong>
                                            @if (!string.IsNullOrEmpty(item.Manufacturer))
                                            {
                                                <br><small class="text-muted">@item.Manufacturer</small>
                                            }
                                        </td>
                                        <td>@Html.DisplayFor(modelItem => item.Description)</td>
                                        <td>
                                            <span class="badge @(item.QuantityInStock <= item.LowStockThreshold ? "bg-warning" : "bg-success")">
                                                @Html.DisplayFor(modelItem => item.QuantityInStock)
                                            </span>
                                        </td>
                                        <td>@item.Price.ToString("C")</td>
                                        <td>
                                            <span class="@(item.ExpirationDate <= DateTime.Now ? "text-danger" : item.ExpirationDate <= DateTime.Now.AddMonths(3) ? "text-warning" : "")">
                                                @item.ExpirationDate.ToString("dd/MM/yyyy")
                                            </span>
                                        </td>
                                        <td>
                                            @if (item.ExpirationDate <= DateTime.Now)
                                            {
                                                <span class="badge bg-danger">Hết hạn</span>
                                            }
                                            else if (item.QuantityInStock <= item.LowStockThreshold)
                                            {
                                                <span class="badge bg-warning">Tồn kho thấp</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-success">Bình thường</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@item.Id" 
                                                   class="btn btn-outline-info btn-sm" title="Xem chi tiết">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-action="Edit" asp-route-id="@item.Id" 
                                                   class="btn btn-outline-primary btn-sm" title="Chỉnh sửa">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a asp-action="Delete" asp-route-id="@item.Id" 
                                                   class="btn btn-outline-danger btn-sm" title="Xóa">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    </script>
}
