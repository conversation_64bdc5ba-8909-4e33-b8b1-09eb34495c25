﻿@model IEnumerable<Midterm1212.Models.Patient>

@{
    ViewData["Title"] = "Danh sách bệnh nhân";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h2>@ViewData["Title"]</h2>
                @if (User.IsInRole("Admin"))
                {
                    <a asp-action="Create" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Thêm bệnh nhân mới
                    </a>
                }
            </div>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Mã BN</th>
                                    <th>Họ và tên</th>
                                    <th>Ngày sinh</th>
                                    <th>Giới tính</th>
                                    <th>Điện thoại</th>
                                    <th>Địa chỉ</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>
                                            <strong>#@Html.DisplayFor(modelItem => item.patient_id)</strong>
                                        </td>
                                        <td>
                                            <strong>@Html.DisplayFor(modelItem => item.name)</strong>
                                        </td>
                                        <td>
                                            @item.dob.ToString("dd/MM/yyyy")
                                            <br><small class="text-muted">@((DateTime.Now - item.dob).Days / 365) tuổi</small>
                                        </td>
                                        <td>
                                            @Html.DisplayFor(modelItem => item.gender)
                                        </td>
                                        <td>
                                            @Html.DisplayFor(modelItem => item.phone)
                                        </td>
                                        <td>
                                            @Html.DisplayFor(modelItem => item.address)
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@item.patient_id"
                                                   class="btn btn-outline-info btn-sm" title="Xem chi tiết">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                @if (User.IsInRole("Admin"))
                                                {
                                                    <a asp-action="Edit" asp-route-id="@item.patient_id"
                                                       class="btn btn-outline-primary btn-sm" title="Chỉnh sửa">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a asp-action="Delete" asp-route-id="@item.patient_id"
                                                       class="btn btn-outline-danger btn-sm" title="Xóa">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                }
                                                @if (User.IsInRole("Doctor"))
                                                {
                                                    <a asp-controller="Appointments" asp-action="Create" asp-route-patientId="@item.patient_id"
                                                       class="btn btn-outline-success btn-sm" title="Tạo lịch hẹn">
                                                        <i class="fas fa-calendar-plus"></i>
                                                    </a>
                                                }
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    </script>
}