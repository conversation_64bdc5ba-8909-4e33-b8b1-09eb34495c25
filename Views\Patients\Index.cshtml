﻿@model IEnumerable<Midterm1212.Models.Patient>

@{
    ViewData["Title"] = "Patients List";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1>Patients List</h1>
            <p>
                <a asp-action="Create" class="btn btn-success">Add New Patient</a>
            </p>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Date of Birth</th>
                        <th>Gender</th>
                        <th>Phone</th>
                        <th>Address</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model)
                    {
                        <tr>
                            <td>
                                @Html.DisplayFor(modelItem => item.name)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.dob)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.gender)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.phone)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.address)
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
    <div class="row mt-4">
        <div class="col-md-12 text-left">
            © 2025 - Student's ID - Your Name
        </div>
    </div>
</div>