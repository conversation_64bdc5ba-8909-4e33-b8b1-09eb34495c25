@model Midterm1212.Models.Patient

@{
    ViewData["Title"] = "<PERSON><PERSON> sơ cá nhân";
}

<div class="container">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-user"></i> @ViewData["Title"]
                    </h4>
                </div>
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-5">Mã bệnh nhân:</dt>
                                <dd class="col-sm-7"><strong>@Html.DisplayFor(model => model.patient_id)</strong></dd>
                                
                                <dt class="col-sm-5">Họ và tên:</dt>
                                <dd class="col-sm-7"><strong>@Html.DisplayFor(model => model.name)</strong></dd>
                                
                                <dt class="col-sm-5">Ngày sinh:</dt>
                                <dd class="col-sm-7">@Model.dob.ToString("dd/MM/yyyy")</dd>
                                
                                <dt class="col-sm-5">Giới tính:</dt>
                                <dd class="col-sm-7">@Html.DisplayFor(model => model.gender)</dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-5">Số điện thoại:</dt>
                                <dd class="col-sm-7">@Html.DisplayFor(model => model.phone)</dd>
                                
                                <dt class="col-sm-5">Địa chỉ:</dt>
                                <dd class="col-sm-7">@Html.DisplayFor(model => model.address)</dd>
                                
                                <dt class="col-sm-5">Tuổi:</dt>
                                <dd class="col-sm-7">@((DateTime.Now - Model.dob).Days / 365) tuổi</dd>
                            </dl>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" asp-controller="Dashboard" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại trang chủ
                        </a>
                        <a asp-action="EditProfile" class="btn btn-primary">
                            <i class="fas fa-edit"></i> Chỉnh sửa thông tin
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Thao tác nhanh</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a asp-action="BookAppointment" class="btn btn-primary">
                            <i class="fas fa-calendar-plus"></i> Đặt lịch hẹn
                        </a>
                        <a asp-action="Appointments" class="btn btn-info">
                            <i class="fas fa-calendar-alt"></i> Lịch hẹn của tôi
                        </a>
                        <a asp-action="MedicalRecords" class="btn btn-success">
                            <i class="fas fa-file-medical"></i> Hồ sơ bệnh án
                        </a>
                        <a asp-action="Prescriptions" class="btn btn-warning">
                            <i class="fas fa-prescription"></i> Đơn thuốc của tôi
                        </a>
                        <a asp-controller="Account" asp-action="ChangePassword" class="btn btn-secondary">
                            <i class="fas fa-key"></i> Đổi mật khẩu
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">Thông tin hệ thống</h6>
                </div>
                <div class="card-body">
                    <p class="mb-2">
                        <i class="fas fa-shield-alt text-success"></i> 
                        Thông tin cá nhân được bảo mật
                    </p>
                    <p class="mb-2">
                        <i class="fas fa-phone text-info"></i> 
                        Hotline: 1900-xxxx
                    </p>
                    <p class="mb-0">
                        <i class="fas fa-envelope text-warning"></i> 
                        Email: <EMAIL>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
