﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Midterm1212.Models;

namespace Midterm1212.Controllers
{
    [Authorize(Roles = "Admin,Doctor")]
    public class AppointmentsController : Controller
    {
        private readonly MedicalDbContext _context;

        public AppointmentsController(MedicalDbContext context)
        {
            _context = context;
        }

        // GET: Appointments
        public async Task<IActionResult> Index()
        {
            var medicalDbContext = _context.Appointments.Include(a => a.Doctor).Include(a => a.Patient);
            return View(await medicalDbContext.ToListAsync());
        }

        // GET: Appointments/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var appointment = await _context.Appointments
                .Include(a => a.Doctor)
                .Include(a => a.Patient)
                .FirstOrDefaultAsync(m => m.appointment_id == id);
            if (appointment == null)
            {
                return NotFound();
            }

            return View(appointment);
        }

        // GET: Appointments/Create
        public async Task<IActionResult> Create()
        {
            // Get doctors with formatted display text
            var doctors = await _context.Doctors
                .Select(d => new {
                    d.doctor_id,
                    DisplayText = $"BS. {d.name} - {d.specialization}"
                })
                .ToListAsync();

            // Get patients with formatted display text
            var patients = await _context.Patients
                .Select(p => new {
                    p.patient_id,
                    DisplayText = $"{p.name} - {p.phone}"
                })
                .ToListAsync();

            // Create SelectList with proper value and text fields
            ViewData["doctor_id"] = new SelectList(doctors, "doctor_id", "DisplayText");
            ViewData["patient_id"] = new SelectList(patients, "patient_id", "DisplayText");

            // Generate new appointment ID
            var maxAppointmentId = await _context.Appointments.AnyAsync()
                ? await _context.Appointments.MaxAsync(a => a.appointment_id)
                : 0;

            var appointment = new Appointment
            {
                appointment_id = maxAppointmentId + 1,
                appointment_date = DateTime.Today.AddDays(1),
                status = "Pending"
            };

            return View(appointment);
        }

        // POST: Appointments/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("appointment_id,patient_id,doctor_id,appointment_date,appointment_time,status")] Appointment appointment)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    // Check if appointment ID already exists
                    var existingAppointment = await _context.Appointments.FindAsync(appointment.appointment_id);
                    if (existingAppointment != null)
                    {
                        ModelState.AddModelError("appointment_id", "Mã lịch hẹn đã tồn tại.");
                        await LoadDropdownsForCreate(appointment);
                        return View(appointment);
                    }

                    // Check for conflicting appointments
                    var conflictingAppointment = await _context.Appointments
                        .AnyAsync(a => a.doctor_id == appointment.doctor_id &&
                                    a.appointment_date.Date == appointment.appointment_date.Date &&
                                    a.appointment_time == appointment.appointment_time &&
                                    a.status != "Cancelled");

                    if (conflictingAppointment)
                    {
                        ModelState.AddModelError("appointment_time", "Bác sĩ đã có lịch hẹn vào thời gian này.");
                        await LoadDropdownsForCreate(appointment);
                        return View(appointment);
                    }

                    _context.Add(appointment);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "Tạo lịch hẹn thành công!";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", "Có lỗi xảy ra khi tạo lịch hẹn: " + ex.Message);
                }
            }

            await LoadDropdownsForCreate(appointment);
            return View(appointment);
        }

        // GET: Appointments/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var appointment = await _context.Appointments.FindAsync(id);
            if (appointment == null)
            {
                return NotFound();
            }
            ViewData["doctor_id"] = new SelectList(_context.Doctors, "doctor_id", "email", appointment.doctor_id);
            ViewData["patient_id"] = new SelectList(_context.Patients, "patient_id", "address", appointment.patient_id);
            return View(appointment);
        }

        // POST: Appointments/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("appointment_id,patient_id,doctor_id,appointment_date,appointment_time,status")] Appointment appointment)
        {
            if (id != appointment.appointment_id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(appointment);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!AppointmentExists(appointment.appointment_id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            ViewData["doctor_id"] = new SelectList(_context.Doctors, "doctor_id", "email", appointment.doctor_id);
            ViewData["patient_id"] = new SelectList(_context.Patients, "patient_id", "address", appointment.patient_id);
            return View(appointment);
        }

        // GET: Appointments/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var appointment = await _context.Appointments
                .Include(a => a.Doctor)
                .Include(a => a.Patient)
                .FirstOrDefaultAsync(m => m.appointment_id == id);
            if (appointment == null)
            {
                return NotFound();
            }

            return View(appointment);
        }

        // POST: Appointments/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var appointment = await _context.Appointments.FindAsync(id);
            if (appointment != null)
            {
                _context.Appointments.Remove(appointment);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        // GET: Appointments/Pending/5
        public async Task<IActionResult> Pending(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var appointment = await _context.Appointments
                .Include(a => a.Doctor)
                .Include(a => a.Patient)
                .FirstOrDefaultAsync(m => m.appointment_id == id);
            if (appointment == null)
            {
                return NotFound();
            }

            ViewBag.Appointment = appointment;
            return View();
        }

        // POST: Appointments/Pending/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Pending(int id, string diagnosis, string prescription, string notes)
        {
            var appointment = await _context.Appointments
                .Include(a => a.Doctor)
                .Include(a => a.Patient)
                .FirstOrDefaultAsync(m => m.appointment_id == id);
            if (appointment == null)
            {
                return NotFound();
            }

            var existingRecord = await _context.MedicalRecords
                .FirstOrDefaultAsync(r => r.appointment_id == id);
            if (existingRecord != null)
            {
                return RedirectToAction("Complete", new { id = id });
            }

            var maxRecordId = await _context.MedicalRecords.AnyAsync()
                ? await _context.MedicalRecords.MaxAsync(r => r.record_id)
                : 0;

            var record = new MedicalRecord
            {
                record_id = maxRecordId + 1,
                patient_id = appointment.patient_id,
                doctor_id = appointment.doctor_id,
                appointment_id = appointment.appointment_id,
                diagnosis = diagnosis,
                prescription = prescription,
                notes = notes,
                record_date = DateTime.Now
            };
            _context.MedicalRecords.Add(record);
            await _context.SaveChangesAsync();

            await _context.Appointments
                .Where(a => a.appointment_id == id)
                .ExecuteUpdateAsync(a => a.SetProperty(p => p.status, "Complete"));
            return RedirectToAction("Complete", new { id = id });
        }

        // GET: Appointments/Complete/5
        public async Task<IActionResult> Complete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var record = await _context.MedicalRecords
                .Include(r => r.Doctor)
                .Include(r => r.Patient)
                .Include(r => r.Appointment)
                .FirstOrDefaultAsync(r => r.appointment_id == id);
            if (record == null)
            {
                return NotFound();
            }

            return View(record);
        }

        // GET: Appointments/CreateMedicalRecord/5
        public async Task<IActionResult> CreateMedicalRecord(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var appointment = await _context.Appointments
                .Include(a => a.Doctor)
                .Include(a => a.Patient)
                .FirstOrDefaultAsync(m => m.appointment_id == id);

            if (appointment == null)
            {
                return NotFound();
            }

            // Check if appointment is confirmed
            if (appointment.status != "Confirmed")
            {
                TempData["ErrorMessage"] = "Chỉ có thể tạo hồ sơ bệnh án cho lịch hẹn đã xác nhận.";
                return RedirectToAction(nameof(Index));
            }

            // Check if medical record already exists
            var existingRecord = await _context.MedicalRecords
                .FirstOrDefaultAsync(r => r.appointment_id == id);
            if (existingRecord != null)
            {
                TempData["ErrorMessage"] = "Lịch hẹn này đã có hồ sơ bệnh án.";
                return RedirectToAction("ViewMedicalRecord", new { id = existingRecord.record_id });
            }

            var maxRecordId = await _context.MedicalRecords.AnyAsync()
                ? await _context.MedicalRecords.MaxAsync(r => r.record_id)
                : 0;

            var medicalRecord = new MedicalRecord
            {
                record_id = maxRecordId + 1,
                patient_id = appointment.patient_id,
                doctor_id = appointment.doctor_id,
                appointment_id = appointment.appointment_id,
                record_date = DateTime.Now
            };

            ViewBag.Appointment = appointment;
            return View(medicalRecord);
        }

        // POST: Appointments/CreateMedicalRecord
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateMedicalRecord([Bind("record_id,patient_id,doctor_id,appointment_id,diagnosis,treatment,notes,record_date")] MedicalRecord medicalRecord)
        {
            try
            {
                // Log the received data
                Console.WriteLine($"Received medical record data: ID={medicalRecord.record_id}, Patient={medicalRecord.patient_id}, Doctor={medicalRecord.doctor_id}, Appointment={medicalRecord.appointment_id}");

                // Get the appointment to validate and get related data
                var appointment = await _context.Appointments
                    .Include(a => a.Doctor)
                    .Include(a => a.Patient)
                    .FirstOrDefaultAsync(m => m.appointment_id == medicalRecord.appointment_id);

                if (appointment == null)
                {
                    TempData["ErrorMessage"] = "Không tìm thấy lịch hẹn.";
                    return RedirectToAction(nameof(Index));
                }

                // Set the navigation properties
                medicalRecord.Patient = appointment.Patient;
                medicalRecord.Doctor = appointment.Doctor;
                medicalRecord.Appointment = appointment;

                // Validate required fields
                if (string.IsNullOrWhiteSpace(medicalRecord.diagnosis))
                {
                    TempData["ErrorMessage"] = "Vui lòng nhập chẩn đoán.";
                    ViewBag.Appointment = appointment;
                    return View(medicalRecord);
                }

                // Check if medical record already exists
                var existingRecord = await _context.MedicalRecords
                    .FirstOrDefaultAsync(r => r.appointment_id == medicalRecord.appointment_id);
                if (existingRecord != null)
                {
                    TempData["ErrorMessage"] = "Lịch hẹn này đã có hồ sơ bệnh án.";
                    return RedirectToAction("ViewMedicalRecord", new { id = existingRecord.record_id });
                }

                _context.Add(medicalRecord);

                // Update appointment status to Complete
                appointment.status = "Complete";
                _context.Update(appointment);

                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = "Tạo hồ sơ bệnh án thành công!";
                return RedirectToAction("CreatePrescription", "Prescriptions", new { medicalRecordId = medicalRecord.record_id });
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Có lỗi xảy ra khi tạo hồ sơ bệnh án: " + ex.Message;
                var appointmentForView = await _context.Appointments
                    .Include(a => a.Doctor)
                    .Include(a => a.Patient)
                    .FirstOrDefaultAsync(m => m.appointment_id == medicalRecord.appointment_id);
                ViewBag.Appointment = appointmentForView;
                return View(medicalRecord);
            }
        }

        // POST: Appointments/UpdateStatus
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateStatus(int id, string status)
        {
            try
            {
                var appointment = await _context.Appointments.FindAsync(id);
                if (appointment == null)
                {
                    return Json(new { success = false, message = "Không tìm thấy lịch hẹn." });
                }

                appointment.status = status;
                _context.Update(appointment);
                await _context.SaveChangesAsync();

                return Json(new { success = true });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Có lỗi xảy ra: " + ex.Message });
            }
        }

        private async Task LoadDropdownsForCreate(Appointment appointment)
        {
            // Get doctors with formatted display text
            var doctors = await _context.Doctors
                .Select(d => new {
                    d.doctor_id,
                    DisplayText = $"BS. {d.name} - {d.specialization}"
                })
                .ToListAsync();

            // Get patients with formatted display text
            var patients = await _context.Patients
                .Select(p => new {
                    p.patient_id,
                    DisplayText = $"{p.name} - {p.phone}"
                })
                .ToListAsync();

            // Create SelectList with proper value and text fields
            ViewData["doctor_id"] = new SelectList(doctors, "doctor_id", "DisplayText", appointment.doctor_id);
            ViewData["patient_id"] = new SelectList(patients, "patient_id", "DisplayText", appointment.patient_id);
        }

        private bool AppointmentExists(int id)
        {
            return _context.Appointments.Any(e => e.appointment_id == id);
        }
    }
}
