﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Midterm1212.Models;

namespace Midterm1212.Controllers
{
    [Authorize(Roles = "Admin,Doctor")]
    public class AppointmentsController : Controller
    {
        private readonly MedicalDbContext _context;

        public AppointmentsController(MedicalDbContext context)
        {
            _context = context;
        }

        // GET: Appointments
        public async Task<IActionResult> Index()
        {
            var medicalDbContext = _context.Appointments.Include(a => a.Doctor).Include(a => a.Patient);
            return View(await medicalDbContext.ToListAsync());
        }

        // GET: Appointments/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var appointment = await _context.Appointments
                .Include(a => a.Doctor)
                .Include(a => a.Patient)
                .FirstOrDefaultAsync(m => m.appointment_id == id);
            if (appointment == null)
            {
                return NotFound();
            }

            return View(appointment);
        }

        // GET: Appointments/Create
        public async Task<IActionResult> Create()
        {
            try
            {
                ViewData["doctor_id"] = new SelectList(
                    await _context.Doctors.Select(d => new {
                        d.doctor_id,
                        DisplayText = $"BS. {d.name} - {d.specialization}"
                    }).ToListAsync(),
                    "doctor_id", "DisplayText");

                ViewData["patient_id"] = new SelectList(
                    await _context.Patients.Select(p => new {
                        p.patient_id,
                        DisplayText = $"{p.name} - {p.phone}"
                    }).ToListAsync(),
                    "patient_id", "DisplayText");

                var maxAppointmentId = await _context.Appointments.AnyAsync()
                    ? await _context.Appointments.MaxAsync(a => a.appointment_id)
                    : 0;

                var appointment = new Appointment
                {
                    appointment_id = maxAppointmentId + 1,
                    appointment_date = DateTime.Today.AddDays(1),
                    status = "Pending"
                };

                return View(appointment);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Có lỗi xảy ra khi tải trang: " + ex.Message;
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: Appointments/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("appointment_id,patient_id,doctor_id,appointment_date,appointment_time,status")] Appointment appointment)
        {
            try
            {
                // Log the received data
                Console.WriteLine($"Received appointment data: ID={appointment.appointment_id}, Patient={appointment.patient_id}, Doctor={appointment.doctor_id}, Date={appointment.appointment_date}, Time={appointment.appointment_time}, Status={appointment.status}");

                // Clear existing model state errors
                ModelState.Clear();

                // Validate required fields
                if (appointment.patient_id <= 0)
                {
                    ModelState.AddModelError("patient_id", "Vui lòng chọn bệnh nhân");
                }
                if (appointment.doctor_id <= 0)
                {
                    ModelState.AddModelError("doctor_id", "Vui lòng chọn bác sĩ");
                }
                if (appointment.appointment_date == default)
                {
                    ModelState.AddModelError("appointment_date", "Vui lòng chọn ngày hẹn");
                }
                if (appointment.appointment_time == default)
                {
                    ModelState.AddModelError("appointment_time", "Vui lòng chọn giờ hẹn");
                }

                if (!ModelState.IsValid)
                {
                    var errors = string.Join("; ", ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage));
                    TempData["ErrorMessage"] = "Dữ liệu không hợp lệ: " + errors;
                    await LoadDropdownsForCreate(appointment);
                    return View(appointment);
                }

                // Check if appointment ID already exists
                var existingAppointment = await _context.Appointments.FindAsync(appointment.appointment_id);
                if (existingAppointment != null)
                {
                    TempData["ErrorMessage"] = "Mã lịch hẹn đã tồn tại.";
                    await LoadDropdownsForCreate(appointment);
                    return View(appointment);
                }

                // Check if patient exists
                var patient = await _context.Patients.FindAsync(appointment.patient_id);
                if (patient == null)
                {
                    TempData["ErrorMessage"] = "Bệnh nhân không tồn tại.";
                    await LoadDropdownsForCreate(appointment);
                    return View(appointment);
                }

                // Check if doctor exists
                var doctor = await _context.Doctors.FindAsync(appointment.doctor_id);
                if (doctor == null)
                {
                    TempData["ErrorMessage"] = "Bác sĩ không tồn tại.";
                    await LoadDropdownsForCreate(appointment);
                    return View(appointment);
                }

                // Check if appointment date is in the past
                if (appointment.appointment_date.Date < DateTime.Today)
                {
                    TempData["ErrorMessage"] = "Không thể đặt lịch hẹn trong quá khứ.";
                    await LoadDropdownsForCreate(appointment);
                    return View(appointment);
                }

                // Check for conflicting appointments
                var conflictingAppointment = await _context.Appointments
                    .AnyAsync(a => a.doctor_id == appointment.doctor_id &&
                                  a.appointment_date.Date == appointment.appointment_date.Date &&
                                  a.appointment_time == appointment.appointment_time &&
                                  a.status != "Cancelled");

                if (conflictingAppointment)
                {
                    TempData["ErrorMessage"] = "Bác sĩ đã có lịch hẹn vào thời gian này.";
                    await LoadDropdownsForCreate(appointment);
                    return View(appointment);
                }

                _context.Add(appointment);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "Tạo lịch hẹn thành công!";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Có lỗi xảy ra khi tạo lịch hẹn: " + ex.Message;
                await LoadDropdownsForCreate(appointment);
                return View(appointment);
            }
        }

        // GET: Appointments/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var appointment = await _context.Appointments.FindAsync(id);
            if (appointment == null)
            {
                return NotFound();
            }

            try
            {
                ViewData["doctor_id"] = new SelectList(
                    await _context.Doctors.Select(d => new {
                        d.doctor_id,
                        DisplayText = $"BS. {d.name} - {d.specialization}"
                    }).ToListAsync(),
                    "doctor_id", "DisplayText", appointment.doctor_id);

                ViewData["patient_id"] = new SelectList(
                    await _context.Patients.Select(p => new {
                        p.patient_id,
                        DisplayText = $"{p.name} - {p.phone}"
                    }).ToListAsync(),
                    "patient_id", "DisplayText", appointment.patient_id);

                return View(appointment);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Có lỗi xảy ra khi tải trang: " + ex.Message;
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: Appointments/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("appointment_id,patient_id,doctor_id,appointment_date,appointment_time,status")] Appointment appointment)
        {
            if (id != appointment.appointment_id)
            {
                return NotFound();
            }

            try
            {
                // Log the received data
                Console.WriteLine($"Received appointment data for edit: ID={appointment.appointment_id}, Patient={appointment.patient_id}, Doctor={appointment.doctor_id}, Date={appointment.appointment_date}, Time={appointment.appointment_time}, Status={appointment.status}");

                // Clear existing model state errors
                ModelState.Clear();

                // Validate required fields
                if (appointment.patient_id <= 0)
                {
                    ModelState.AddModelError("patient_id", "Vui lòng chọn bệnh nhân");
                }
                if (appointment.doctor_id <= 0)
                {
                    ModelState.AddModelError("doctor_id", "Vui lòng chọn bác sĩ");
                }
                if (appointment.appointment_date == default)
                {
                    ModelState.AddModelError("appointment_date", "Vui lòng chọn ngày hẹn");
                }
                if (appointment.appointment_time == default)
                {
                    ModelState.AddModelError("appointment_time", "Vui lòng chọn giờ hẹn");
                }

                if (!ModelState.IsValid)
                {
                    var errors = string.Join("; ", ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage));
                    TempData["ErrorMessage"] = "Dữ liệu không hợp lệ: " + errors;
                    await LoadDropdownsForEdit(appointment);
                    return View(appointment);
                }

                // Get the original appointment to check status
                var originalAppointment = await _context.Appointments.AsNoTracking().FirstOrDefaultAsync(a => a.appointment_id == id);
                if (originalAppointment == null)
                {
                    return NotFound();
                }

                // Check if trying to change status of completed or cancelled appointment
                if ((originalAppointment.status == "Complete" || originalAppointment.status == "Cancelled") && 
                    originalAppointment.status != appointment.status)
                {
                    TempData["ErrorMessage"] = "Không thể thay đổi trạng thái của lịch hẹn đã hoàn thành hoặc đã hủy.";
                    await LoadDropdownsForEdit(appointment);
                    return View(appointment);
                }

                // Check if patient exists
                var patient = await _context.Patients.FindAsync(appointment.patient_id);
                if (patient == null)
                {
                    TempData["ErrorMessage"] = "Bệnh nhân không tồn tại.";
                    await LoadDropdownsForEdit(appointment);
                    return View(appointment);
                }

                // Check if doctor exists
                var doctor = await _context.Doctors.FindAsync(appointment.doctor_id);
                if (doctor == null)
                {
                    TempData["ErrorMessage"] = "Bác sĩ không tồn tại.";
                    await LoadDropdownsForEdit(appointment);
                    return View(appointment);
                }

                // Check if appointment date is in the past
                if (appointment.appointment_date.Date < DateTime.Today)
                {
                    TempData["ErrorMessage"] = "Không thể đặt lịch hẹn trong quá khứ.";
                    await LoadDropdownsForEdit(appointment);
                    return View(appointment);
                }

                // Check for conflicting appointments (excluding current appointment)
                var conflictingAppointment = await _context.Appointments
                    .AnyAsync(a => a.doctor_id == appointment.doctor_id &&
                                  a.appointment_date.Date == appointment.appointment_date.Date &&
                                  a.appointment_time == appointment.appointment_time &&
                                  a.status != "Cancelled" &&
                                  a.appointment_id != appointment.appointment_id);

                if (conflictingAppointment)
                {
                    TempData["ErrorMessage"] = "Bác sĩ đã có lịch hẹn vào thời gian này.";
                    await LoadDropdownsForEdit(appointment);
                    return View(appointment);
                }

                try
                {
                    _context.Update(appointment);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "Cập nhật lịch hẹn thành công!";
                    return RedirectToAction(nameof(Index));
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!AppointmentExists(appointment.appointment_id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Có lỗi xảy ra khi cập nhật lịch hẹn: " + ex.Message;
                await LoadDropdownsForEdit(appointment);
                return View(appointment);
            }
        }

        // GET: Appointments/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var appointment = await _context.Appointments
                .Include(a => a.Doctor)
                .Include(a => a.Patient)
                .FirstOrDefaultAsync(m => m.appointment_id == id);
            if (appointment == null)
            {
                return NotFound();
            }

            return View(appointment);
        }

        // POST: Appointments/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var appointment = await _context.Appointments.FindAsync(id);
            if (appointment != null)
            {
                _context.Appointments.Remove(appointment);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        // GET: Appointments/Pending/5
        public async Task<IActionResult> Pending(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var appointment = await _context.Appointments
                .Include(a => a.Doctor)
                .Include(a => a.Patient)
                .FirstOrDefaultAsync(m => m.appointment_id == id);
            if (appointment == null)
            {
                return NotFound();
            }

            ViewBag.Appointment = appointment;
            return View();
        }

        // POST: Appointments/Pending/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Pending(int id, string diagnosis, string prescription, string notes)
        {
            var appointment = await _context.Appointments
                .Include(a => a.Doctor)
                .Include(a => a.Patient)
                .FirstOrDefaultAsync(m => m.appointment_id == id);
            if (appointment == null)
            {
                return NotFound();
            }

            var existingRecord = await _context.MedicalRecords
                .FirstOrDefaultAsync(r => r.appointment_id == id);
            if (existingRecord != null)
            {
                return RedirectToAction("Complete", new { id = id });
            }

            var maxRecordId = await _context.MedicalRecords.AnyAsync()
                ? await _context.MedicalRecords.MaxAsync(r => r.record_id)
                : 0;

            var record = new MedicalRecord
            {
                record_id = maxRecordId + 1,
                patient_id = appointment.patient_id,
                doctor_id = appointment.doctor_id,
                appointment_id = appointment.appointment_id,
                diagnosis = diagnosis,
                prescription = prescription,
                notes = notes,
                record_date = DateTime.Now
            };
            _context.MedicalRecords.Add(record);
            await _context.SaveChangesAsync();

            await _context.Appointments
                .Where(a => a.appointment_id == id)
                .ExecuteUpdateAsync(a => a.SetProperty(p => p.status, "Complete"));
            return RedirectToAction("Complete", new { id = id });
        }

        // GET: Appointments/Complete/5
        public async Task<IActionResult> Complete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var record = await _context.MedicalRecords
                .Include(r => r.Doctor)
                .Include(r => r.Patient)
                .Include(r => r.Appointment)
                .FirstOrDefaultAsync(r => r.appointment_id == id);
            if (record == null)
            {
                return NotFound();
            }

            return View(record);
        }

        private async Task LoadDropdownsForCreate(Appointment appointment)
        {
            try
            {
                ViewData["doctor_id"] = new SelectList(
                    await _context.Doctors.Select(d => new {
                        d.doctor_id,
                        DisplayText = $"BS. {d.name} - {d.specialization}"
                    }).ToListAsync(),
                    "doctor_id", "DisplayText", appointment.doctor_id);

                ViewData["patient_id"] = new SelectList(
                    await _context.Patients.Select(p => new {
                        p.patient_id,
                        DisplayText = $"{p.name} - {p.phone}"
                    }).ToListAsync(),
                    "patient_id", "DisplayText", appointment.patient_id);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading dropdowns: {ex.Message}");
                throw;
            }
        }

        private async Task LoadDropdownsForEdit(Appointment appointment)
        {
            try
            {
                ViewData["doctor_id"] = new SelectList(
                    await _context.Doctors.Select(d => new {
                        d.doctor_id,
                        DisplayText = $"BS. {d.name} - {d.specialization}"
                    }).ToListAsync(),
                    "doctor_id", "DisplayText", appointment.doctor_id);

                ViewData["patient_id"] = new SelectList(
                    await _context.Patients.Select(p => new {
                        p.patient_id,
                        DisplayText = $"{p.name} - {p.phone}"
                    }).ToListAsync(),
                    "patient_id", "DisplayText", appointment.patient_id);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading dropdowns: {ex.Message}");
                throw;
            }
        }

        private bool AppointmentExists(int id)
        {
            return _context.Appointments.Any(e => e.appointment_id == id);
        }
    }
}
