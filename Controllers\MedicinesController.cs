using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Midterm1212.Models;

namespace Midterm1212.Controllers
{
    [Authorize(Roles = "Admin")]
    public class MedicinesController : Controller
    {
        private readonly MedicalDbContext _context;

        public MedicinesController(MedicalDbContext context)
        {
            _context = context;
        }

        public async Task<IActionResult> Index(string searchString, string sortOrder)
        {
            ViewData["NameSortParm"] = String.IsNullOrEmpty(sortOrder) ? "name_desc" : "";
            ViewData["PriceSortParm"] = sortOrder == "Price" ? "price_desc" : "Price";
            ViewData["StockSortParm"] = sortOrder == "Stock" ? "stock_desc" : "Stock";
            ViewData["ExpiryDateSortParm"] = sortOrder == "ExpiryDate" ? "expiry_desc" : "ExpiryDate";
            ViewData["CurrentFilter"] = searchString;

            var medicines = from m in _context.Medicines select m;

            if (!String.IsNullOrEmpty(searchString))
            {
                medicines = medicines.Where(m => m.Name.Contains(searchString) 
                                              || m.Description.Contains(searchString)
                                              || m.Manufacturer.Contains(searchString));
            }

            switch (sortOrder)
            {
                case "name_desc":
                    medicines = medicines.OrderByDescending(m => m.Name);
                    break;
                case "Price":
                    medicines = medicines.OrderBy(m => m.Price);
                    break;
                case "price_desc":
                    medicines = medicines.OrderByDescending(m => m.Price);
                    break;
                case "Stock":
                    medicines = medicines.OrderBy(m => m.QuantityInStock);
                    break;
                case "stock_desc":
                    medicines = medicines.OrderByDescending(m => m.QuantityInStock);
                    break;
                case "ExpiryDate":
                    medicines = medicines.OrderBy(m => m.ExpirationDate);
                    break;
                case "expiry_desc":
                    medicines = medicines.OrderByDescending(m => m.ExpirationDate);
                    break;
                default:
                    medicines = medicines.OrderBy(m => m.Name);
                    break;
            }

            ViewBag.LowStockCount = await _context.Medicines
                .CountAsync(m => m.QuantityInStock <= m.LowStockThreshold);
            ViewBag.ExpiredCount = await _context.Medicines
                .CountAsync(m => m.ExpirationDate <= DateTime.Now);

            return View(await medicines.AsNoTracking().ToListAsync());
        }

        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var medicine = await _context.Medicines
                .Include(m => m.PrescriptionDetails)
                .ThenInclude(pd => pd.Prescription)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (medicine == null)
            {
                return NotFound();
            }

            return View(medicine);
        }

        public IActionResult Create()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,Name,Description,QuantityInStock,ExpirationDate,Price,Manufacturer,Dosage,Form,LowStockThreshold")] Medicine medicine)
        {
            if (ModelState.IsValid)
            {
                var existingMedicine = await _context.Medicines.FindAsync(medicine.Id);
                if (existingMedicine != null)
                {
                    ModelState.AddModelError("Id", "Mã thuốc đã tồn tại.");
                    return View(medicine);
                }

                medicine.CreatedDate = DateTime.Now;
                _context.Add(medicine);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "Thuốc đã được thêm thành công.";
                return RedirectToAction(nameof(Index));
            }
            return View(medicine);
        }

        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var medicine = await _context.Medicines.FindAsync(id);
            if (medicine == null)
            {
                return NotFound();
            }
            return View(medicine);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Name,Description,QuantityInStock,ExpirationDate,Price,Manufacturer,Dosage,Form,LowStockThreshold,CreatedDate")] Medicine medicine)
        {
            if (id != medicine.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(medicine);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "Thông tin thuốc đã được cập nhật thành công.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!MedicineExists(medicine.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(medicine);
        }

        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var medicine = await _context.Medicines
                .FirstOrDefaultAsync(m => m.Id == id);
            if (medicine == null)
            {
                return NotFound();
            }

            return View(medicine);
        }

        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var medicine = await _context.Medicines.FindAsync(id);
            if (medicine != null)
            {
                var hasRelatedRecords = await _context.PrescriptionDetails
                    .AnyAsync(pd => pd.MedicineId == id);

                if (hasRelatedRecords)
                {
                    TempData["ErrorMessage"] = "Không thể xóa thuốc này vì đã có đơn thuốc liên quan.";
                    return RedirectToAction(nameof(Index));
                }

                _context.Medicines.Remove(medicine);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "Thuốc đã được xóa thành công.";
            }
            return RedirectToAction(nameof(Index));
        }

        public async Task<IActionResult> LowStock()
        {
            var lowStockMedicines = await _context.Medicines
                .Where(m => m.QuantityInStock <= m.LowStockThreshold)
                .OrderBy(m => m.QuantityInStock)
                .ToListAsync();

            return View(lowStockMedicines);
        }

        public async Task<IActionResult> Expired()
        {
            var expiredMedicines = await _context.Medicines
                .Where(m => m.ExpirationDate <= DateTime.Now)
                .OrderBy(m => m.ExpirationDate)
                .ToListAsync();

            return View(expiredMedicines);
        }

        private bool MedicineExists(int id)
        {
            return _context.Medicines.Any(e => e.Id == id);
        }
    }
}
