using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Midterm1212.Models
{
    public class InventoryAlert
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        [Required]
        public int alert_id { get; set; }

        [Required]
        [Display(Name = "Thuố<PERSON>")]
        public int medication_id { get; set; }

        [Required]
        [Display(Name = "Loại cảnh báo")]
        public string alert_type { get; set; } = string.Empty; // <PERSON><PERSON><PERSON> h<PERSON>, <PERSON><PERSON><PERSON> kho thấp, <PERSON><PERSON><PERSON> hàng

        [Required]
        [Display(Name = "<PERSON>ứ<PERSON> độ ưu tiên")]
        public string priority_level { get; set; } = "Trung bình"; // Th<PERSON><PERSON>, <PERSON>rung bình, Cao, Kh<PERSON>n cấp

        [Required]
        [Display(Name = "Thông điệp")]
        public string message { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Tr<PERSON>ng thái")]
        public string status { get; set; } = "Chưa xử lý"; // <PERSON><PERSON><PERSON>ử lý, <PERSON><PERSON><PERSON> lý, <PERSON><PERSON>ử lý, Bỏ qua

        [Required]
        [Display(Name = "<PERSON><PERSON><PERSON> tạo")]
        public DateTime created_date { get; set; } = DateTime.Now;

        [Display(Name = "Ngày xử lý")]
        public DateTime? resolved_date { get; set; }

        [Display(Name = "Người xử lý")]
        public string? resolved_by { get; set; }

        [Display(Name = "Ghi chú xử lý")]
        public string? resolution_notes { get; set; }

        [ForeignKey("medication_id")]
        public virtual Medication Medication { get; set; }
    }
}
