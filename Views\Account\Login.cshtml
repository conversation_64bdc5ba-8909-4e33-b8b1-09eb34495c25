@model Midterm1212.Models.ViewModels.LoginViewModel

@{
    ViewData["Title"] = "Đăng nhập";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-sign-in-alt"></i> @ViewData["Title"]
                    </h4>
                </div>
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <form asp-action="Login" asp-route-returnurl="@ViewData["ReturnUrl"]" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <div class="mb-3">
                            <label asp-for="UserName" class="form-label">Email hoặc tên đăng nhập</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                <input asp-for="UserName" class="form-control" placeholder="Nhập email hoặc tên đăng nhập" />
                            </div>
                            <span asp-validation-for="UserName" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Password" class="form-label"></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input asp-for="Password" class="form-control" placeholder="Nhập mật khẩu" />
                            </div>
                            <span asp-validation-for="Password" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="UserType" class="form-label"></label>
                            <select asp-for="UserType" class="form-select">
                                <option value="">-- Tất cả vai trò --</option>
                                <option value="@UserType.Patient">Bệnh nhân</option>
                                <option value="@UserType.Doctor">Bác sĩ</option>
                                <option value="@UserType.Admin">Quản trị viên</option>
                            </select>
                            <div class="form-text">Tùy chọn: Chọn vai trò để đăng nhập nhanh hơn</div>
                        </div>

                        <div class="mb-3 form-check">
                            <input asp-for="RememberMe" class="form-check-input" />
                            <label asp-for="RememberMe" class="form-check-label"></label>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt"></i> Đăng nhập
                            </button>
                        </div>
                    </form>

                    <hr>

                    <div class="text-center">
                        <p class="mb-2">Chưa có tài khoản?</p>
                        <a asp-action="Register" class="btn btn-outline-success">
                            <i class="fas fa-user-plus"></i> Đăng ký bệnh nhân
                        </a>
                    </div>

                    <hr>

                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Tài khoản mặc định</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Quản trị viên:</strong><br>
                                    <small>Tên đăng nhập: <code>00000000</code></small><br>
                                    <small>Mật khẩu: <code>123456</code></small>
                                </div>
                                <div class="col-md-6">
                                    <strong>Bác sĩ:</strong><br>
                                    <small>Email: <code><EMAIL></code></small><br>
                                    <small>Mật khẩu: <code>123123</code></small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}