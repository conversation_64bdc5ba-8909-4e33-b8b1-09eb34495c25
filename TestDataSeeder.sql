-- Test Data for Workflow Testing
-- Run this script to create test data for the appointment -> medical record -> prescription -> COD order workflow

-- Insert test medicines
INSERT INTO Medicines (Id, Name, Description, QuantityInStock, ExpirationDate, Price, Manufacturer, Dosage, Form, CreatedDate, LowStockThreshold) VALUES
(1, 'Paracetamol 500mg', 'Thuốc giảm đau, hạ sốt', 100, '2025-12-31', 5000, 'Traphaco', '500mg', 'Viên nén', GETDATE(), 10),
(2, 'Amoxicillin 250mg', '<PERSON><PERSON><PERSON>g sinh điều trị nhiễm khuẩn', 50, '2025-06-30', 15000, 'Imexpharm', '250mg', 'Viên nang', GETDATE(), 10),
(3, 'Vitamin C 1000mg', '<PERSON>ổ sung vitamin C', 200, '2026-01-31', 8000, 'DHG Pharma', '1000mg', '<PERSON>i<PERSON><PERSON> sủi', GETDATE(), 20),
(4, 'Omeprazole 20mg', '<PERSON><PERSON><PERSON><PERSON> điều trị loét dạ dày', 75, '2025-09-15', 12000, 'Stada', '20mg', 'Viên nang', GETDATE(), 15),
(5, 'Cetirizine 10mg', 'Thuốc chống dị ứng', 80, '2025-11-20', 6000, 'Pymepharco', '10mg', 'Viên nén', GETDATE(), 10);

-- Insert test patients (if not exists)
IF NOT EXISTS (SELECT 1 FROM Patients WHERE patient_id = 1)
INSERT INTO Patients (patient_id, name, dob, gender, phone, email, address) VALUES
(1, 'Nguyễn Văn An', '1985-03-15', 'Nam', '**********', '<EMAIL>', '123 Đường ABC, Quận 1, TP.HCM');

IF NOT EXISTS (SELECT 1 FROM Patients WHERE patient_id = 2)
INSERT INTO Patients (patient_id, name, dob, gender, phone, email, address) VALUES
(2, 'Trần Thị Bình', '1990-07-22', 'Nữ', '**********', '<EMAIL>', '456 Đường XYZ, Quận 3, TP.HCM');

-- Insert test doctors (if not exists)
IF NOT EXISTS (SELECT 1 FROM Doctors WHERE doctor_id = 1)
INSERT INTO Doctors (doctor_id, name, specialization, phone, email, schedule) VALUES
(1, 'BS. Lê Văn Cường', 'Nội khoa', '**********', '<EMAIL>', 'Thứ 2-6: 8:00-17:00');

IF NOT EXISTS (SELECT 1 FROM Doctors WHERE doctor_id = 2)
INSERT INTO Doctors (doctor_id, name, specialization, phone, email, schedule) VALUES
(2, 'BS. Phạm Thị Dung', 'Nhi khoa', '**********', '<EMAIL>', 'Thứ 2-6: 8:00-17:00');

-- Insert test appointments
DECLARE @NextAppointmentId INT = (SELECT ISNULL(MAX(appointment_id), 0) + 1 FROM Appointments);

INSERT INTO Appointments (appointment_id, patient_id, doctor_id, appointment_date, appointment_time, status) VALUES
(@NextAppointmentId, 1, 1, CAST(GETDATE() AS DATE), '09:00:00', 'Confirmed'),
(@NextAppointmentId + 1, 2, 2, CAST(GETDATE() AS DATE), '10:00:00', 'Confirmed'),
(@NextAppointmentId + 2, 1, 2, CAST(DATEADD(day, 1, GETDATE()) AS DATE), '14:00:00', 'Pending');

PRINT 'Test data has been inserted successfully!';
PRINT 'You can now test the workflow:';
PRINT '1. Go to Appointments -> Index';
PRINT '2. Click "Hoàn tất" on a Confirmed appointment';
PRINT '3. Create Medical Record';
PRINT '4. Create Prescription with medicines';
PRINT '5. Create COD order from prescription';
