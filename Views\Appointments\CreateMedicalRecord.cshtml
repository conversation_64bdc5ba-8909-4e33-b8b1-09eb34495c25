@model Midterm1212.Models.MedicalRecord

@{
    ViewData["Title"] = "Tạo <PERSON>ồ sơ bệnh án";
    var appointment = ViewBag.Appointment as Midterm1212.Models.Appointment;
}

<div class="container">
    <div class="row">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-file-medical"></i> @ViewData["Title"]
                    </h4>
                </div>
                <div class="card-body">
                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            @TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <form asp-action="CreateMedicalRecord" method="post" id="medicalRecordForm">
                        @Html.AntiForgeryToken()
                        <div asp-validation-summary="All" class="text-danger mb-3"></div>
                        
                        <input type="hidden" asp-for="record_id" />
                        <input type="hidden" asp-for="patient_id" value="@appointment?.Patient?.patient_id" />
                        <input type="hidden" asp-for="doctor_id" value="@appointment?.Doctor?.doctor_id" />
                        <input type="hidden" asp-for="appointment_id" value="@appointment?.appointment_id" />
                        <input type="hidden" asp-for="record_date" />

                        <!-- Add hidden fields for navigation properties -->
                        <input type="hidden" name="Patient.patient_id" value="@appointment?.Patient?.patient_id" />
                        <input type="hidden" name="Patient.name" value="@appointment?.Patient?.name" />
                        <input type="hidden" name="Patient.dob" value="@appointment?.Patient?.dob.ToString("yyyy-MM-dd")" />
                        <input type="hidden" name="Patient.gender" value="@appointment?.Patient?.gender" />
                        <input type="hidden" name="Patient.phone" value="@appointment?.Patient?.phone" />

                        <input type="hidden" name="Doctor.doctor_id" value="@appointment?.Doctor?.doctor_id" />
                        <input type="hidden" name="Doctor.name" value="@appointment?.Doctor?.name" />
                        <input type="hidden" name="Doctor.specialization" value="@appointment?.Doctor?.specialization" />

                        <input type="hidden" name="Appointment.appointment_id" value="@appointment?.appointment_id" />
                        <input type="hidden" name="Appointment.appointment_date" value="@appointment?.appointment_date.ToString("yyyy-MM-dd")" />
                        <input type="hidden" name="Appointment.appointment_time" value="@appointment?.appointment_time" />
                        <input type="hidden" name="Appointment.status" value="@appointment?.status" />

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Mã hồ sơ</label>
                                    <input value="@Model.record_id" class="form-control" readonly />
                                    <div class="form-text">Mã hồ sơ được tạo tự động</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Ngày khám</label>
                                    <input value="@Model.record_date.ToString("dd/MM/yyyy HH:mm")" class="form-control" readonly />
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="diagnosis" class="form-label">Chẩn đoán</label>
                            <textarea asp-for="diagnosis" class="form-control" rows="3" placeholder="Nhập chẩn đoán bệnh..." required maxlength="500"></textarea>
                            <span asp-validation-for="diagnosis" class="text-danger"></span>
                            <small class="text-muted">Tối đa 500 ký tự</small>
                        </div>

                        <div class="mb-3">
                            <label asp-for="treatment" class="form-label">Điều trị</label>
                            <textarea asp-for="treatment" class="form-control" rows="4" placeholder="Nhập phương pháp điều trị..." maxlength="1000"></textarea>
                            <span asp-validation-for="treatment" class="text-danger"></span>
                            <small class="text-muted">Tối đa 1000 ký tự</small>
                        </div>

                        <div class="mb-3">
                            <label asp-for="notes" class="form-label">Ghi chú</label>
                            <textarea asp-for="notes" class="form-control" rows="3" placeholder="Ghi chú thêm (nếu có)..." maxlength="1000"></textarea>
                            <span asp-validation-for="notes" class="text-danger"></span>
                            <small class="text-muted">Tối đa 1000 ký tự</small>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Lưu ý:</h6>
                            <ul class="mb-0">
                                <li>Sau khi tạo hồ sơ bệnh án, bạn sẽ được chuyển đến trang kê đơn thuốc</li>
                                <li>Hồ sơ bệnh án sẽ được lưu vĩnh viễn trong hệ thống</li>
                                <li>Thông tin chẩn đoán và điều trị cần chính xác và chi tiết</li>
                            </ul>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại danh sách
                            </a>
                            <button type="submit" class="btn btn-success" id="submitBtn">
                                <i class="fas fa-save"></i> Lưu hồ sơ và kê đơn thuốc
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Thông tin lịch hẹn</h6>
                </div>
                <div class="card-body">
                    @if (appointment != null)
                    {
                        <dl class="row">
                            <dt class="col-sm-5">Mã lịch hẹn:</dt>
                            <dd class="col-sm-7">#@appointment.appointment_id</dd>
                            
                            <dt class="col-sm-5">Ngày hẹn:</dt>
                            <dd class="col-sm-7">@appointment.appointment_date.ToString("dd/MM/yyyy")</dd>
                            
                            <dt class="col-sm-5">Giờ hẹn:</dt>
                            <dd class="col-sm-7">@appointment.appointment_time.ToString(@"hh\:mm")</dd>
                            
                            <dt class="col-sm-5">Trạng thái:</dt>
                            <dd class="col-sm-7">
                                <span class="badge bg-info">@appointment.status</span>
                            </dd>
                        </dl>
                    }
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">Thông tin bệnh nhân</h6>
                </div>
                <div class="card-body">
                    @if (appointment?.Patient != null)
                    {
                        <dl class="row">
                            <dt class="col-sm-5">Mã BN:</dt>
                            <dd class="col-sm-7">#@appointment.Patient.patient_id</dd>
                            
                            <dt class="col-sm-5">Họ tên:</dt>
                            <dd class="col-sm-7"><strong>@appointment.Patient.name</strong></dd>
                            
                            <dt class="col-sm-5">Tuổi:</dt>
                            <dd class="col-sm-7">@((DateTime.Now - appointment.Patient.dob).Days / 365) tuổi</dd>
                            
                            <dt class="col-sm-5">Giới tính:</dt>
                            <dd class="col-sm-7">@appointment.Patient.gender</dd>
                            
                            <dt class="col-sm-5">Điện thoại:</dt>
                            <dd class="col-sm-7">@appointment.Patient.phone</dd>
                        </dl>
                    }
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">Thông tin bác sĩ</h6>
                </div>
                <div class="card-body">
                    @if (appointment?.Doctor != null)
                    {
                        <dl class="row">
                            <dt class="col-sm-5">Bác sĩ:</dt>
                            <dd class="col-sm-7"><strong>BS. @appointment.Doctor.name</strong></dd>
                            
                            <dt class="col-sm-5">Chuyên khoa:</dt>
                            <dd class="col-sm-7">@appointment.Doctor.specialization</dd>
                        </dl>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // Form validation
            $('#medicalRecordForm').on('submit', function(e) {
                var isValid = true;
                var firstError = null;

                // Check required fields
                $(this).find('[required]').each(function() {
                    if (!$(this).val()) {
                        isValid = false;
                        $(this).addClass('is-invalid');
                        if (!firstError) {
                            firstError = $(this);
                        }
                    } else {
                        $(this).removeClass('is-invalid');
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                    if (firstError) {
                        firstError.focus();
                    }
                    return false;
                }

                // Disable submit button to prevent double submission
                $('#submitBtn').prop('disabled', true);
            });

            // Remove invalid class when user starts typing
            $('textarea').on('input', function() {
                $(this).removeClass('is-invalid');
            });
        
        // Character count for textareas
        $('textarea').on('input', function() {
            const maxLength = $(this).attr('maxlength');
            if (maxLength) {
                const currentLength = $(this).val().length;
                const remaining = maxLength - currentLength;
                
                let countElement = $(this).siblings('.char-count');
                if (countElement.length === 0) {
                    countElement = $('<small class="char-count text-muted"></small>');
                    $(this).after(countElement);
                }
                
                countElement.text(`${currentLength}/${maxLength} ký tự`);
                
                if (remaining < 50) {
                    countElement.removeClass('text-muted').addClass('text-warning');
                }
                if (remaining < 10) {
                    countElement.removeClass('text-warning').addClass('text-danger');
                }
            }
        });
        });

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    </script>
}
