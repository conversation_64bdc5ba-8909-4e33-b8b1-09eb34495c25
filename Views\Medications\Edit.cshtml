@model Midterm1212.Models.Medication

@{
    ViewData["Title"] = "Chỉnh sửa thuốc";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1>Chỉnh sửa thuốc</h1>

            <form asp-action="Edit" method="post">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                
                <input type="hidden" asp-for="medication_id" />
                <input type="hidden" asp-for="created_date" />
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="medication_id" class="control-label">Mã thuốc</label>
                            <input asp-for="medication_id" class="form-control" readonly />
                        </div>

                        <div class="form-group">
                            <label asp-for="medication_name" class="control-label">Tên thuốc</label>
                            <input asp-for="medication_name" class="form-control" />
                            <span asp-validation-for="medication_name" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="active_ingredient" class="control-label">Thành phần hoạt chất</label>
                            <input asp-for="active_ingredient" class="form-control" />
                            <span asp-validation-for="active_ingredient" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="dosage" class="control-label">Liều lượng</label>
                            <input asp-for="dosage" class="form-control" placeholder="VD: 500mg, 10ml..." />
                            <span asp-validation-for="dosage" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="form" class="control-label">Dạng thuốc</label>
                            <select asp-for="form" class="form-control">
                                <option value="">-- Chọn dạng thuốc --</option>
                                <option value="Viên nén">Viên nén</option>
                                <option value="Viên nang">Viên nang</option>
                                <option value="Siro">Siro</option>
                                <option value="Thuốc tiêm">Thuốc tiêm</option>
                                <option value="Thuốc nhỏ mắt">Thuốc nhỏ mắt</option>
                                <option value="Thuốc bôi">Thuốc bôi</option>
                                <option value="Khác">Khác</option>
                            </select>
                            <span asp-validation-for="form" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="manufacturer" class="control-label">Nhà sản xuất</label>
                            <input asp-for="manufacturer" class="form-control" />
                            <span asp-validation-for="manufacturer" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="category_id" class="control-label">Danh mục</label>
                            <select asp-for="category_id" class="form-control" asp-items="ViewBag.category_id">
                                <option value="">-- Chọn danh mục --</option>
                            </select>
                            <span asp-validation-for="category_id" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="stock_quantity" class="control-label">Số lượng tồn kho</label>
                            <input asp-for="stock_quantity" class="form-control" type="number" min="0" />
                            <span asp-validation-for="stock_quantity" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="minimum_stock" class="control-label">Số lượng tối thiểu</label>
                            <input asp-for="minimum_stock" class="form-control" type="number" min="0" />
                            <span asp-validation-for="minimum_stock" class="text-danger"></span>
                            <small class="form-text text-muted">Hệ thống sẽ cảnh báo khi tồn kho xuống dưới mức này</small>
                        </div>

                        <div class="form-group">
                            <label asp-for="unit_price" class="control-label">Giá bán (VNĐ)</label>
                            <input asp-for="unit_price" class="form-control" type="number" min="0" step="1000" />
                            <span asp-validation-for="unit_price" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="manufacture_date" class="control-label">Ngày sản xuất</label>
                            <input asp-for="manufacture_date" class="form-control" type="date" />
                            <span asp-validation-for="manufacture_date" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="expiry_date" class="control-label">Ngày hết hạn</label>
                            <input asp-for="expiry_date" class="form-control" type="date" />
                            <span asp-validation-for="expiry_date" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="notes" class="control-label">Ghi chú</label>
                            <textarea asp-for="notes" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="notes" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label class="control-label">Ngày tạo</label>
                            <input value="@Model.created_date.ToString("dd/MM/yyyy HH:mm")" class="form-control" readonly />
                        </div>

                        @if (Model.updated_date.HasValue)
                        {
                            <div class="form-group">
                                <label class="control-label">Ngày cập nhật cuối</label>
                                <input value="@Model.updated_date.Value.ToString("dd/MM/yyyy HH:mm")" class="form-control" readonly />
                            </div>
                        }
                    </div>
                </div>

                <div class="form-group">
                    <input type="submit" value="Cập nhật" class="btn btn-primary" />
                    <a asp-action="Index" class="btn btn-secondary">Quay lại</a>
                    <a asp-action="Details" asp-route-id="@Model.medication_id" class="btn btn-info">Chi tiết</a>
                </div>
            </form>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-md-12 text-left">
            © 2025 - Student's ID - Your Name
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Validate expiry date is after manufacture date
        $('#expiry_date').change(function() {
            var manufactureDate = new Date($('#manufacture_date').val());
            var expiryDate = new Date($(this).val());
            
            if (expiryDate <= manufactureDate) {
                alert('Ngày hết hạn phải sau ngày sản xuất');
                $(this).val('');
            }
        });

        // Validate minimum stock is less than or equal to stock quantity
        $('#stock_quantity, #minimum_stock').change(function() {
            var stockQuantity = parseInt($('#stock_quantity').val()) || 0;
            var minimumStock = parseInt($('#minimum_stock').val()) || 0;
            
            if (minimumStock > stockQuantity) {
                alert('Số lượng tối thiểu không được lớn hơn số lượng tồn kho');
                $('#minimum_stock').val(stockQuantity);
            }
        });
    </script>
}
