﻿@model Midterm1212.Models.Appointment

@{
    ViewData["Title"] = "Tạo lịch hẹn mới";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-calendar-plus"></i> @ViewData["Title"]
                    </h4>
                </div>
                <div class="card-body">
                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle"></i> @TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle"></i> @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <form asp-action="Create" method="post" id="createAppointmentForm">
                        <div asp-validation-summary="All" class="text-danger mb-3"></div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="appointment_id" class="form-label">Mã lịch hẹn</label>
                                    <input asp-for="appointment_id" class="form-control" readonly />
                                    <span asp-validation-for="appointment_id" class="text-danger"></span>
                                    <div class="form-text">Mã lịch hẹn được tạo tự động</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="appointment_date" class="form-label">Ngày hẹn</label>
                                    <input asp-for="appointment_date" class="form-control" type="date" required />
                                    <span asp-validation-for="appointment_date" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="patient_id" class="form-label">Bệnh nhân</label>
                                    <select asp-for="patient_id" asp-items="ViewBag.patient_id" class="form-select" required>
                                        <option value="">-- Chọn bệnh nhân --</option>
                                    </select>
                                    <span asp-validation-for="patient_id" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="doctor_id" class="form-label">Bác sĩ</label>
                                    <select asp-for="doctor_id" asp-items="ViewBag.doctor_id" class="form-select" required>
                                        <option value="">-- Chọn bác sĩ --</option>
                                    </select>
                                    <span asp-validation-for="doctor_id" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="appointment_time" class="form-label">Giờ hẹn</label>
                                    <select asp-for="appointment_time" class="form-select" required>
                                        <option value="">-- Chọn giờ hẹn --</option>
                                        <option value="08:00:00">08:00</option>
                                        <option value="08:30:00">08:30</option>
                                        <option value="09:00:00">09:00</option>
                                        <option value="09:30:00">09:30</option>
                                        <option value="10:00:00">10:00</option>
                                        <option value="10:30:00">10:30</option>
                                        <option value="11:00:00">11:00</option>
                                        <option value="11:30:00">11:30</option>
                                        <option value="14:00:00">14:00</option>
                                        <option value="14:30:00">14:30</option>
                                        <option value="15:00:00">15:00</option>
                                        <option value="15:30:00">15:30</option>
                                        <option value="16:00:00">16:00</option>
                                        <option value="16:30:00">16:30</option>
                                        <option value="17:00:00">17:00</option>
                                    </select>
                                    <span asp-validation-for="appointment_time" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="status" class="form-label">Trạng thái</label>
                                    <select asp-for="status" class="form-select" required>
                                        <option value="Pending">Chờ xác nhận</option>
                                        <option value="Confirmed">Đã xác nhận</option>
                                    </select>
                                    <span asp-validation-for="status" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Lưu ý:</h6>
                            <ul class="mb-0">
                                <li>Kiểm tra kỹ thông tin bệnh nhân và bác sĩ trước khi tạo</li>
                                <li>Đảm bảo bác sĩ không có lịch hẹn trùng thời gian</li>
                                <li>Lịch hẹn có thể được chỉnh sửa sau khi tạo</li>
                            </ul>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại danh sách
                            </a>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-save"></i> Tạo lịch hẹn
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        // Set minimum date to today
        const today = new Date();
        document.getElementById('appointment_date').min = today.toISOString().split('T')[0];

        // Set max date to 30 days from now
        const maxDate = new Date();
        maxDate.setDate(maxDate.getDate() + 30);
        document.getElementById('appointment_date').max = maxDate.toISOString().split('T')[0];

        // Form validation
        $(document).ready(function() {
            $('#createAppointmentForm').on('submit', function(e) {
                var isValid = true;
                var firstError = null;

                // Check required fields
                $(this).find('[required]').each(function() {
                    if (!$(this).val()) {
                        isValid = false;
                        $(this).addClass('is-invalid');
                        if (!firstError) {
                            firstError = $(this);
                        }
                    } else {
                        $(this).removeClass('is-invalid');
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                    if (firstError) {
                        firstError.focus();
                    }
                    return false;
                }
            });

            // Remove invalid class when user starts typing/selecting
            $('input, select').on('input change', function() {
                $(this).removeClass('is-invalid');
            });
        });

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    </script>
}
